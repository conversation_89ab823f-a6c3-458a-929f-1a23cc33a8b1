# apn-flink-etl

## 业务背景

APN 系统的报表功能，是通过构造 SQL 查询 MySQL 实现的 ，但是 MySQL 作为 OLTP 型数据库，不适合做这些 OLAP 查询操作，因此在动态报表需求中选择了
Starrocks 作为 OLAP 查询引擎

选择 Flink CDC 作为数据同步工具，基于 Flink CDC 3.2.0 的 jar 包，做了一些自定义处理，包括设置合理的数据分区分桶键、简单的数据清洗（去除外键为
null 的 binlog 记录）等

## 模块说明

- `core`: 核心模块，封装了自定义的 StarRocksSchema 类以及相关工具，以及数据库表的元数据信息和自定义操作
- `flink-cdc-cli-resource`: 直接使用 Flink CDC 需要用到的 jar 包
- `flink-cdc-job`: 基于 Flink CDC 实现的 MySQL 到 StarRocks 的数据同步任务
- `helm-charts`: Helm Charts 部署 StarRocks、Flink CDC Pipeline 到 Kubernetes 集群等需要的 YAML 文件
- `starrocks-initial`: 初始化 StarRocks 表结构的逻辑，包括 ODS、DWD 两个 main class

## 架构

![img.png](img/architecture.png)

## 部署

### 先决条件 Requirement

需要保证mysql用户有读取 binlog 的权限

- REPLICATION CLIENT
- REPLICATION SLAVE

编辑 MySQL 配置文件 my.cnf（默认路径为 /etc/my.cnf），以开启 MySQL Binlog

```
# 开启 Binlog 日志
log_bin = ON
# 设置 Binlog 的存储位置
log_bin =/var/lib/mysql/mysql-bin
# 设置 server_id 
# 在 MySQL 5.7.3 及以后版本，如果没有 server_id，那么设置 binlog 后无法开启 MySQL 服务 
server_id = 1
# 设置 Binlog 模式为 ROW
binlog_format = ROW
# binlog 日志的基本文件名，后面会追加标识来表示每一个 Binlog 文件
log_bin_basename =/var/lib/mysql/mysql-bin
# binlog 文件的索引文件，管理所有 Binlog 文件的目录
log_bin_index =/var/lib/mysql/mysql-bin.index
```

### 部署StarRocks

[使用 Helm 部署 StarRocks 集群 | StarRocks](https://docs.starrocks.io/zh/docs/deployment/helm/)

1. 添加 StarRocks 的 Helm Chart Repo。Helm Chart 包括 StarRocks Operator 和定制资源 StarRocksCluster 的定义。
2. 添加 Helm Chart Repo。

   ```helm repo add starrocks https://starrocks.github.io/starrocks-kubernetes-operator```
3. 更新 Helm Chart Repo 至最新版本。
   helm repo update
4. 查看所添加的 Helm Chart Repo。
   $ helm search repo starrocks
   NAME CHART VERSION APP VERSION DESCRIPTION
   starrocks/kube-starrocks 1.8.0 3.1-latest kube-starrocks includes two subcharts, starrock...
   starrocks/operator 1.8.0 1.8.0 A Helm chart for StarRocks operator
   starrocks/starrocks 1.8.0 3.1-latest A Helm chart for StarRocks cluster
5. 自定义配置文件，覆盖 values.yaml 配置



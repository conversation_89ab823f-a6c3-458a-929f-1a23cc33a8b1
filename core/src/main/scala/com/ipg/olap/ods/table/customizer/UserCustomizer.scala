package com.ipg.olap.ods.table.customizer

import com.ipg.olap.ods.OdsMetaData
import com.ipg.olap.ods.customizer.Constants.GRANULARITY_MONTH
import com.ipg.olap.ods.customizer.{CustomizeHelper, TableCustomizer}
import com.ipg.olap.ods.table.transformer.TableTransFormer
import com.ipg.olap.scmema.StarRocksTable
import org.apache.flink.cdc.common.event.TableId
import org.apache.flink.cdc.composer.definition.TransformDef
import org.apache.flink.cdc.runtime.operators.transform.TransformRule

class UserCustomizer extends TableCustomizer with TableTransFormer {

  private val USER_ID = "user_id"

  override def customize(table: StarRocksTable): StarRocksTable = {
    table.getTableName match {
      case OdsMetaData.USER_ACCOUNT_TABLE =>
        CustomizeHelper.specifyKeyAndPartition(table, USER_ID, GRANULARITY_MONTH)
      case OdsMetaData.USER_JOB_RELATION_TABLE
           | OdsMetaData.USER_ROLE_TABLE =>
        CustomizeHelper.specifyKey(table, USER_ID)
      case _ => table
    }
  }

  override def isCustomizable(tableName: String): Boolean =
    OdsMetaData.userTables.contains(tableName)

  override def transform(tableId: TableId): Option[TransformDef] = {
    if (!isCustomizable(tableId.getTableName)) {
      return None
    }

    tableId.getTableName match {
      case OdsMetaData.USER_ACCOUNT_TABLE
           | OdsMetaData.USER_JOB_RELATION_TABLE
           | OdsMetaData.USER_ROLE_TABLE => fieldNowNullRule(tableId, USER_ID)
      case _ => None
    }
  }
}
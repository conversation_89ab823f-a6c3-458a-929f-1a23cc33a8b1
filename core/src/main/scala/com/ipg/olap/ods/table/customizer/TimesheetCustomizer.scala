package com.ipg.olap.ods.table.customizer

import com.ipg.olap.ods.OdsMetaData
import com.ipg.olap.ods.customizer.Constants.GRANULARITY_MONTH
import com.ipg.olap.ods.customizer.{CustomizeHelper, TableCustomizer}
import com.ipg.olap.ods.table.transformer.TableTransFormer
import com.ipg.olap.scmema.StarRocksTable
import org.apache.flink.cdc.common.event.TableId
import org.apache.flink.cdc.composer.definition.TransformDef
import org.apache.flink.cdc.runtime.operators.transform.TransformRule

class TimesheetCustomizer extends TableCustomizer with TableTransFormer {

  private val ASSIGNMENT_ID = "assignment_id"

  override def customize(table: StarRocksTable): StarRocksTable = {
    table.getTableName match {
      case OdsMetaData.TIME_SHEET_EXPENSE_WEEK_ENDING_RECORD_TABLE
           | OdsMetaData.TIME_SHEET_HOLIDAY_RECORD_TABLE
           | OdsMetaData.TIME_SHEET_RECORD_TABLE
           | OdsMetaData.TIME_SHEET_WEEK_ENDING_RECORD_TABLE
           | OdsMetaData.TIMESHEET_BREAKTIME_RECORD_TABLE
           | OdsMetaData.TIMESHEET_EXPENSE_RECORD_TABLE =>
        CustomizeHelper.specifyKeyAndPartition(table, ASSIGNMENT_ID, GRANULARITY_MONTH)
      case OdsMetaData.TIMESHEET_MANAGER_TABLE
           | OdsMetaData.TIMESHEET_COMMENTS_TABLE =>
        CustomizeHelper.specifyKey(table, ASSIGNMENT_ID)
      case _ => table
    }
  }

  override def isCustomizable(tableName: String): Boolean = {
    OdsMetaData.timesheetTables.contains(tableName)
  }

  override def transform(tableId: TableId): Option[TransformDef] = {
    if (!isCustomizable(tableId.getTableName)) {
      return None
    }
    tableId.getTableName match {
      case OdsMetaData.TIME_SHEET_EXPENSE_WEEK_ENDING_RECORD_TABLE
           | OdsMetaData.TIME_SHEET_HOLIDAY_RECORD_TABLE
           | OdsMetaData.TIME_SHEET_RECORD_TABLE
           | OdsMetaData.TIME_SHEET_WEEK_ENDING_RECORD_TABLE
           | OdsMetaData.TIMESHEET_BREAKTIME_RECORD_TABLE
           | OdsMetaData.TIMESHEET_EXPENSE_RECORD_TABLE
           | OdsMetaData.TIMESHEET_MANAGER_TABLE
           | OdsMetaData.TIMESHEET_COMMENTS_TABLE => fieldNowNullRule(tableId, ASSIGNMENT_ID)
      case _ => None
    }
  }
}
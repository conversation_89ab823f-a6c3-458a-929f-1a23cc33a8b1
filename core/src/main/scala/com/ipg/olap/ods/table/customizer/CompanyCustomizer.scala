package com.ipg.olap.ods.table.customizer

import com.ipg.olap.ods.OdsMetaData
import com.ipg.olap.ods.customizer.Constants.GRANULARITY_MONTH
import com.ipg.olap.ods.customizer.{CustomizeHelper, TableCustomizer}
import com.ipg.olap.ods.table.transformer.TableTransFormer
import com.ipg.olap.scmema.StarRocksTable
import org.apache.flink.cdc.common.event.TableId
import org.apache.flink.cdc.composer.definition.TransformDef

class CompanyCustomizer extends TableCustomizer with TableTransFormer {

  private val COMPANY_ID = "company_id"

  override def customize(table: StarRocksTable): StarRocksTable = {
    table.getTableName match {
      case OdsMetaData.COMPANY_INDUSTRY_RELATION_TABLE
           | OdsMetaData.COMPANY_LOCATION_TABLE
           | OdsMetaData.COMPANY_ADDITIONAL_INFO_TABLE
           | OdsMetaData.COMPANY_USER_RELATION_TABLE =>
        CustomizeHelper.specifyKey(table, COMPANY_ID)
      case OdsMetaData.COMPANY_PROJECT_TEAM_TABLE
           | OdsMetaData.COMPANY_SALES_LEAD_CLIENT_CONTACT_TABLE
           | OdsMetaData.COMPANY_CLIENT_NOTE
           | OdsMetaData.ACCOUNT_BUSINESS
           | OdsMetaData.COMPANY_BUSINESS_FLOW_ADMINISTRATOR_TABLE =>
        CustomizeHelper.specifyKeyAndPartition(table, COMPANY_ID, GRANULARITY_MONTH)
      case _ => table
    }
  }

  override def isCustomizable(tableName: String): Boolean = {
    OdsMetaData.companyTables.contains(tableName)
  }

  override def transform(tableId: TableId): Option[TransformDef] = {
    if (!isCustomizable(tableId.getTableName)) {
      return None
    }
    tableId.getTableName match {
      case OdsMetaData.COMPANY_INDUSTRY_RELATION_TABLE
           | OdsMetaData.COMPANY_LOCATION_TABLE
           | OdsMetaData.COMPANY_ADDITIONAL_INFO_TABLE
           | OdsMetaData.COMPANY_USER_RELATION_TABLE
           | OdsMetaData.COMPANY_PROJECT_TEAM_TABLE
           | OdsMetaData.COMPANY_CLIENT_NOTE
           | OdsMetaData.ACCOUNT_BUSINESS
           | OdsMetaData.COMPANY_BUSINESS_FLOW_ADMINISTRATOR_TABLE
           | OdsMetaData.COMPANY_SALES_LEAD_CLIENT_CONTACT_TABLE => fieldNowNullRule(tableId, COMPANY_ID)
      case _ => None
    }
  }
}
package com.ipg.olap.ods.table.customizer

import com.ipg.olap.ods.OdsMetaData
import com.ipg.olap.ods.customizer.Constants.GRANULARITY_MONTH
import com.ipg.olap.ods.customizer.{CustomizeHelper, TableCustomizer}
import com.ipg.olap.ods.table.transformer.TableTransFormer
import com.ipg.olap.scmema.StarRocksTable
import org.apache.flink.cdc.common.event.TableId
import org.apache.flink.cdc.composer.definition.TransformDef
import org.apache.flink.cdc.runtime.operators.transform.TransformRule

class ContractorInvoiceCustomizer extends TableCustomizer with TableTransFormer {

  private val INVOICE_ID = "invoice_id"
  private val GROUP_INVOICE_ID = "group_invoice_id"

  override def customize(table: StarRocksTable): StarRocksTable = {
    table.getTableName match {
      case OdsMetaData.GROUP_INVOICE_RECORD_TABLE
           | OdsMetaData.INVOICE_EXPENSE_INFO_TABLE
           | OdsMetaData.INVOICE_TIMESHEET_INFO_TABLE =>
        CustomizeHelper.specifyKeyAndPartition(table, INVOICE_ID, GRANULARITY_MONTH)
      case OdsMetaData.RECORD_PAYMENT_INFO_TABLE =>
        CustomizeHelper.specifyKeyAndPartition(table, GROUP_INVOICE_ID, GRANULARITY_MONTH)
      case _ => table
    }
  }

  override def isCustomizable(tableName: String): Boolean = {
    OdsMetaData.contractorInvoiceTables.contains(tableName)
  }

  override def transform(tableId: TableId): Option[TransformDef] = {
    if (!isCustomizable(tableId.getTableName)) {
      return None
    }
    tableId.getTableName match {
      case OdsMetaData.GROUP_INVOICE_RECORD_TABLE
           | OdsMetaData.INVOICE_EXPENSE_INFO_TABLE
           | OdsMetaData.INVOICE_TIMESHEET_INFO_TABLE => fieldNowNullRule(tableId, INVOICE_ID)
      case OdsMetaData.RECORD_PAYMENT_INFO_TABLE => fieldNowNullRule(tableId, GROUP_INVOICE_ID)
      case _ => None
    }
  }
}
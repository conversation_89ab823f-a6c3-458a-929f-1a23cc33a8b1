package com.ipg.olap.ods.table.customizer

import com.ipg.olap.ods.OdsMetaData
import com.ipg.olap.ods.customizer.Constants.GRANULARITY_MONTH
import com.ipg.olap.ods.customizer.{CustomizeHelper, TableCustomizer}
import com.ipg.olap.ods.table.transformer.TableTransFormer
import com.ipg.olap.scmema.StarRocksTable
import org.apache.flink.cdc.common.event.TableId
import org.apache.flink.cdc.composer.definition.TransformDef

class TalentCustomizer extends TableCustomizer with TableTransFormer {

  private val TALENT_ID = "talent_id"

  override def customize(table: StarRocksTable): StarRocksTable = {
    table.getTableName match {
      case OdsMetaData.TALENT_CONTACT_TABLE
           | OdsMetaData.TALENT_CURRENT_LOCATION_TABLE
           | OdsMetaData.TALENT_OWNERSHIP_TABLE
           | OdsMetaData.TALENT_RESUME_RELATION_TABLE
           | OdsMetaData.TALENT_NOTE_TABLE
           | OdsMetaData.TALENT_WORK_AUTHORIZATION_RELATION_TABLE =>
        CustomizeHelper.specifyKeyAndPartition(table, TALENT_ID, GRANULARITY_MONTH)
      case OdsMetaData.TALENT_USER_RELATION_TABLE =>
        CustomizeHelper.specifyKey(table, TALENT_ID)
      case _ => table
    }
  }

  override def isCustomizable(tableName: String): Boolean = {
    OdsMetaData.talentTables.contains(tableName)
  }

  override def transform(tableId: TableId): Option[TransformDef] = {
    if (!isCustomizable(tableId.getTableName)) {
      return None
    }

    tableId.getTableName match {
      case OdsMetaData.TALENT_CONTACT_TABLE
           | OdsMetaData.TALENT_CURRENT_LOCATION_TABLE
           | OdsMetaData.TALENT_OWNERSHIP_TABLE
           | OdsMetaData.TALENT_NOTE_TABLE
           | OdsMetaData.TALENT_RESUME_RELATION_TABLE
           | OdsMetaData.TALENT_WORK_AUTHORIZATION_RELATION_TABLE
           | OdsMetaData.TALENT_USER_RELATION_TABLE => fieldNowNullRule(tableId, TALENT_ID)
      case _ => None
    }
  }
}
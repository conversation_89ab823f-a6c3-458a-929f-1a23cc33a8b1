package com.ipg.olap.ods.table.customizer

import com.ipg.olap.ods.OdsMetaData
import com.ipg.olap.ods.customizer.Constants.GRANULARITY_MONTH
import com.ipg.olap.ods.customizer.{CustomizeHelper, TableCustomizer}
import com.ipg.olap.ods.table.transformer.TableTransFormer
import com.ipg.olap.scmema.StarRocksTable
import org.apache.flink.cdc.common.event.TableId
import org.apache.flink.cdc.composer.definition.TransformDef
import org.apache.flink.cdc.runtime.operators.transform.TransformRule

class InvoiceCustomizer extends TableCustomizer with TableTransFormer{

  private val INVOICE_ID = "invoice_id"

  override def customize(table: StarRocksTable): StarRocksTable = {
    table.getTableName match {
      case OdsMetaData.INVOICE_PAYMENT_RECORD_TABLE =>
        CustomizeHelper.specifyKeyAndPartition(table, INVOICE_ID, GRA<PERSON><PERSON>RITY_MONTH)
      case _ => table
    }
  }

  override def isCustomizable(tableName: String): Boolean = {
    OdsMetaData.invoiceTables.contains(tableName)
  }

  override def transform(tableId: TableId): Option[TransformDef] = {
    if (!isCustomizable(tableId.getTableName)) {
      return None
    }

    tableId.getTableName match {
      case OdsMetaData.INVOICE_ACTIVITY_TABLE | OdsMetaData.INVOICE_PAYMENT_RECORD_TABLE => fieldNowNullRule(tableId, INVOICE_ID)
      case _ => None
    }

  }
}
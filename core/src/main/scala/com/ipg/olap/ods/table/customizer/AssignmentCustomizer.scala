package com.ipg.olap.ods.table.customizer

import com.ipg.olap.ods.OdsMetaData
import com.ipg.olap.ods.customizer.{CustomizeHelper, TableCustomizer}
import com.ipg.olap.ods.table.transformer.TableTransFormer
import com.ipg.olap.scmema.StarRocksTable
import org.apache.flink.cdc.common.event.TableId
import org.apache.flink.cdc.composer.definition.TransformDef
import org.apache.flink.cdc.runtime.operators.transform.TransformRule

class AssignmentCustomizer extends TableCustomizer with TableTransFormer{
  private val ASSIGNMENT_ID = "assignment_id"

  override def customize(table: StarRocksTable): StarRocksTable = {
    CustomizeHelper.specifyKey(table, ASSIGNMENT_ID)
  }

  override def isCustomizable(tableName: String): Boolean = {
    OdsMetaData.assignmentDetailTables.contains(tableName)
  }

  override def transform(tableId: TableId): Option[TransformDef] = {
    if (!isCustomizable(tableId.getTableName)) {
      return None
    }
    fieldNowNullRule(tableId, ASSIGNMENT_ID)
  }
}
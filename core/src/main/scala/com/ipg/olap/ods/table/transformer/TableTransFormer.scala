package com.ipg.olap.ods.table.transformer

import com.ipg.olap.ods.table.customizer._
import org.apache.flink.cdc.common.event.TableId
import org.apache.flink.cdc.composer.definition.TransformDef

import scala.collection.JavaConverters.{asScalaBufferConverter, seqAsJavaListConverter}

trait TableTransFormer {

  def transform(tableId: TableId): Option[TransformDef]


  def fieldNowNullRule(tableId: TableId, field: String): Option[TransformDef] = {
    Some(new TransformDef(tableId.toString, "*", s"$field IS NOT NULL", "", "", "", "", ""))
  }
}

object TableTransFormer {

  private val transFormers: List[TableTransFormer] = List(
    new ApplicationCustomizer(),
    new AssignmentCustomizer(),
    new CompanyCustomizer(),
    new ContractorInvoiceCustomizer(),
    new InvoiceCustomizer(),
    new JobCustomizer(),
    new StartCustomizer(),
    new TalentCustomizer(),
    new TimesheetCustomizer(),
    new UserCustomizer()
  )

  def doTransformAsJava(tableIds: java.util.List[TableId]): java.util.List[TransformDef] = {
    tableIds.asScala.flatMap(tableId => {
      transFormers.map(transformer => transformer.transform(tableId))
        .filter(_.isDefined)
        .map(_.get)
    }).asJava
  }
}

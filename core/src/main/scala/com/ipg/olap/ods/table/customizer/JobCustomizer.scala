package com.ipg.olap.ods.table.customizer

import com.ipg.olap.ods.OdsMetaData
import com.ipg.olap.ods.customizer.Constants.GRANULARITY_MONTH
import com.ipg.olap.ods.customizer.{CustomizeHelper, TableCustomizer}
import com.ipg.olap.ods.table.transformer.TableTransFormer
import com.ipg.olap.scmema.StarRocksTable
import org.apache.flink.cdc.common.event.TableId
import org.apache.flink.cdc.composer.definition.TransformDef

class JobCustomizer extends TableCustomizer with TableTransFormer {
  private val JOB_ID = "job_id"

  override def customize(table: StarRocksTable): StarRocksTable = {
    table.getTableName match {
      case OdsMetaData.JOB_JOB_FUNCTION_RELATION_TABLE
           | OdsMetaData.JOB_USER_RELATION_TABLE
           | OdsMetaData.JOB_NOTE
           | OdsMetaData.JOB_LOCATION_TABLE =>
        CustomizeHelper.specifyKey(table, JOB_ID)
      case OdsMetaData.TALENT_RECRUITMENT_PROCESS_AUTO_ELIMINATION_TABLE =>
        CustomizeHelper.specifyKeyAndPartition(table, JOB_ID, GRANULARITY_MONTH)
      case _ => table
    }
  }

  override def isCustomizable(tableName: String): Boolean = {
    OdsMetaData.jobTables.contains(tableName)
  }

  override def transform(tableId: TableId): Option[TransformDef] = {
    if (!isCustomizable(tableId.getTableName)) {
      return None
    }

    tableId.getTableName match {
      case OdsMetaData.JOB_JOB_FUNCTION_RELATION_TABLE
           | OdsMetaData.JOB_USER_RELATION_TABLE
           | OdsMetaData.JOB_NOTE
           | OdsMetaData.JOB_LOCATION_TABLE
           | OdsMetaData.TALENT_RECRUITMENT_PROCESS_AUTO_ELIMINATION_TABLE => fieldNowNullRule(tableId, JOB_ID)
      case _ => None
    }

  }
}
package com.ipg.olap.ods.table.customizer

import com.ipg.olap.ods.OdsMetaData
import com.ipg.olap.ods.customizer.Constants.GRANULARITY_MONTH
import com.ipg.olap.ods.customizer.{CustomizeHelper, TableCustomizer}
import com.ipg.olap.ods.table.transformer.TableTransFormer
import com.ipg.olap.scmema.StarRocksTable
import org.apache.flink.cdc.common.event.TableId
import org.apache.flink.cdc.composer.definition.TransformDef
import org.apache.flink.cdc.runtime.operators.transform.TransformRule

class StartCustomizer extends TableCustomizer with TableTransFormer {

  private val START_ID = "start_id"

  override def customize(table: StarRocksTable): StarRocksTable = {
    table.getTableName match {
      case OdsMetaData.START_ADDRESS_TABLE
           | OdsMetaData.START_CLIENT_INFO_TABLE
           | OdsMetaData.START_COMMISSION_TABLE
           | OdsMetaData.START_CONTRACT_RATE_TABLE
           | OdsMetaData.START_FAILED_WARRANTY_TABLE
           | OdsMetaData.START_FTE_RATE_TABLE
           | OdsMetaData.START_FTE_SALARY_PACKAGE_TABLE
           | OdsMetaData.START_RATE_CHANGE_TABLE
           | OdsMetaData.START_RESIDENTIAL_ADDRESS_TABLE
           | OdsMetaData.START_TERMINATION_TABLE =>
        CustomizeHelper.specifyKeyAndPartition(table, START_ID, GRANULARITY_MONTH)
      case _ => table
    }
  }

  override def isCustomizable(tableName: String): Boolean = {
    OdsMetaData.startTables.contains(tableName)
  }

  override def transform(tableId: TableId): Option[TransformDef] = {
    if (!isCustomizable(tableId.getTableName)) {
      return None
    }

    tableId.getTableName match {
      case OdsMetaData.START_ADDRESS_TABLE
           | OdsMetaData.START_CLIENT_INFO_TABLE
           | OdsMetaData.START_COMMISSION_TABLE
           | OdsMetaData.START_CONTRACT_RATE_TABLE
           | OdsMetaData.START_FAILED_WARRANTY_TABLE
           | OdsMetaData.START_FTE_RATE_TABLE
           | OdsMetaData.START_FTE_SALARY_PACKAGE_TABLE
           | OdsMetaData.START_RATE_CHANGE_TABLE
           | OdsMetaData.START_RESIDENTIAL_ADDRESS_TABLE
           | OdsMetaData.START_TERMINATION_TABLE => fieldNowNullRule(tableId, START_ID)
      case _ => None
    }
  }
}
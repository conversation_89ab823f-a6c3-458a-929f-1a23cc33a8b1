package com.ipg.olap.ods.table.customizer

import com.ipg.olap.ods.OdsMetaData
import com.ipg.olap.ods.customizer.Constants.GRANULARITY_MONTH
import com.ipg.olap.ods.customizer.{CustomizeHelper, TableCustomizer}
import com.ipg.olap.ods.table.transformer.TableTransFormer
import com.ipg.olap.scmema.StarRocksTable
import org.apache.flink.cdc.common.event.TableId
import org.apache.flink.cdc.composer.definition.TransformDef

class ApplicationCustomizer extends TableCustomizer with TableTransFormer {

  private val TALENT_RECRUITMENT_PROCESS_ID = "talent_recruitment_process_id"

  override def customize(table: StarRocksTable): StarRocksTable = {
    table.getTableName match {
      case OdsMetaData.TALENT_RECRUITMENT_PROCESS_COMMISSION_TABLE
           | OdsMetaData.TALENT_RECRUITMENT_PROCESS_ELIMINATE_TABLE
           | OdsMetaData.TALENT_RECRUITMENT_PROCESS_INTERVIEW_TABLE
           | OdsMetaData.TALENT_RECRUITMENT_PROCESS_IPG_AGREED_PAY_RATE_TABLE
           | OdsMetaData.TALENT_RECRUITMENT_PROCESS_IPG_CONTRACT_FEE_CHARGE_TABLE
           | OdsMetaData.TALENT_RECRUITMENT_PROCESS_IPG_FTE_SALARY_PACKAGE_TABLE
           | OdsMetaData.TALENT_RECRUITMENT_PROCESS_IPG_OFFER_ACCEPT_TABLE
           | OdsMetaData.TALENT_RECRUITMENT_PROCESS_KPI_USER_TABLE
           | OdsMetaData.TALENT_RECRUITMENT_PROCESS_NODE_TABLE
           | OdsMetaData.TALENT_RECRUITMENT_PROCESS_NOTE_TABLE
           | OdsMetaData.TALENT_RECRUITMENT_PROCESS_OFFER_TABLE
           | OdsMetaData.TALENT_RECRUITMENT_PROCESS_OFFER_FEE_CHARGE_TABLE
           | OdsMetaData.TALENT_RECRUITMENT_PROCESS_OFFER_SALARY_TABLE
           | OdsMetaData.TALENT_RECRUITMENT_PROCESS_OFFER_SALARY_PACKAGE_TABLE
           | OdsMetaData.TALENT_RECRUITMENT_PROCESS_ONBOARD_TABLE
           | OdsMetaData.TALENT_RECRUITMENT_PROCESS_ONBOARD_CLIENT_INFO_TABLE
           | OdsMetaData.TALENT_RECRUITMENT_PROCESS_ONBOARD_DATE_TABLE
           | OdsMetaData.TALENT_RECRUITMENT_PROCESS_RESIGNATION_TABLE
           | OdsMetaData.TALENT_RECRUITMENT_PROCESS_STOP_STATISTICS_TABLE
           | OdsMetaData.TALENT_RECRUITMENT_PROCESS_SUBMIT_TO_CLIENT_TABLE
           | OdsMetaData.TALENT_RECRUITMENT_PROCESS_SUBMIT_TO_JOB_TABLE => CustomizeHelper.specifyKeyAndPartition(table, TALENT_RECRUITMENT_PROCESS_ID, GRANULARITY_MONTH)
      case OdsMetaData.TALENT_RECRUITMENT_PROCESS_ONBOARD_WORK_LOCATION_TABLE => CustomizeHelper.specifyKey(table, TALENT_RECRUITMENT_PROCESS_ID)
      case _ => table
    }
  }

  override def isCustomizable(tableName: String): Boolean = OdsMetaData.talentRecruitmentProcessTables.contains(tableName)

  override def transform(tableId: TableId): Option[TransformDef] = {
    if (!isCustomizable(tableId.getTableName)) {
      return None
    }
    tableId.getTableName match {
      case OdsMetaData.TALENT_RECRUITMENT_PROCESS_TABLE
           | OdsMetaData.TALENT_RECRUITMENT_PROCESS_IPG_OFFER_LETTER_COST_RATE_TABLE => None
      case _ => fieldNowNullRule(tableId, TALENT_RECRUITMENT_PROCESS_ID)
    }
  }
}

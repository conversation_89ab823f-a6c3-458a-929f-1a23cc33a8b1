package com.ipg.olap.dwd.table.customizer

import com.ipg.olap.dwd.{DwdMetaData, DwdTable}
import com.ipg.olap.dwd.customizer.DwdTableCustomizer

class CompanyCustomizer extends DwdTableCustomizer{

  override def customize(table: DwdTable): String = {
    //        String createViewSql = """
    //            CREATE MATERIALIZED VIEW IF NOT EXISTS %s.%s
    //                   DISTRIBUTED BY HASH(`id`)
    //            REFRESH
    //            		IMMEDIATE
    //            		ASYNC EVERY (INTERVAL 60 second)
    //            PARTITION BY created_date
    //            AS
    //            """;
    val createViewSql = s"""
            CREATE VIEW IF NOT EXISTS ${DwdMetaData.DWD_DATABASE}.${table.getName}
            AS
            """
    s"""
            $createViewSql
            SELECT company.*,
                   additional.extended_info,
                   get_json_string(PARSE_JSON(additional.extended_info), '$$fortuneRank')            AS fortune_rank,
                   get_json_string(PARSE_JSON(additional.extended_info), '$$.revenueCurrency')        AS revenue_currency,
                   get_json_string(PARSE_JSON(additional.extended_info), '$$.linkedinCompanyProfile') AS linkedin_company_profile,
                   CAST(get_json_string(PARSE_JSON(additional.extended_info), '$$.aliases') AS json)  AS aliases,
                   get_json_double(PARSE_JSON(additional.extended_info), '$$.staffsNumber.gte')       AS staffs_number_gte,
                   get_json_double(PARSE_JSON(additional.extended_info), '$$.staffsNumber.lte')       AS staffs_numbe_lte,
                   get_json_string(PARSE_JSON(additional.extended_info), '$$.organizationName')       AS organization_name,
                   get_json_string(PARSE_JSON(additional.extended_info), '$$.profile')                AS profile,
                   get_json_string(PARSE_JSON(additional.extended_info), '$$.websites')               AS websites,
                   get_json_double(PARSE_JSON(additional.extended_info), '$$.annualTurnover.gte')     AS annual_turnover_gte,
                   get_json_double(PARSE_JSON(additional.extended_info), '$$.annualTurnover.lte')     AS annual_turnover_lte,
                   get_json_int(PARSE_JSON(additional.extended_info), '$$.companyClientLevel')        AS company_client_level,
                   get_json_string(PARSE_JSON(additional.extended_info),
                                   '$$.crunchbaseCompanyProfile')                                     AS crunchbase_company_profile,
                   CAST(get_json_string(PARSE_JSON(additional.extended_info), '$$.contacts') AS json) AS contacts
            FROM ods_apn.company
            LEFT JOIN ods_apn.company_additional_info  AS additional ON ods_apn.company.id = additional.company_id;
            """.stripMargin
  }
}

package com.ipg.olap.dwd.table.customizer

import com.ipg.olap.dwd.DwdMetaData
import com.ipg.olap.dwd.DwdTable
import com.ipg.olap.dwd.customizer.DwdTableCustomizer

class TalentCustomizer extends DwdTableCustomizer {
  override def customize(table: DwdTable): String = {
    s"""
       CREATE MATERIALIZED VIEW IF NOT EXISTS ${DwdMetaData.DWD_DATABASE}.${table.getName}
       DISTRIBUTED BY HASH(id)
       REFRESH
         IMMEDIATE
         ASYNC EVERY (INTERVAL 60 second)
       PARTITION BY created_date
       AS
       SELECT
           talent.*,
           get_json_double(PARSE_JSON(additional.extended_info),'$$.currencyUSDExchangeRate') as currency_usd_exchange_rate,
           get_json_double(PARSE_JSON(additional.extended_info),'$$.salaryRange.gte') AS salary_range_gte,
           get_json_double(PARSE_JSON(additional.extended_info),'$$.salaryRange.lte') AS salary_range_lte,
           CAST(get_json_string(PARSE_JSON(additional.extended_info),'$$.projects') AS json) AS projects,
           get_json_string(PARSE_JSON(additional.extended_info),'$$.preferredCurrency') AS preferred_currency,
           CAST(get_json_string(PARSE_JSON(additional.extended_info),'$$.educations') AS json) AS educations,
           get_json_string(PARSE_JSON(additional.extended_info),'$$.source') AS source,
           CAST(get_json_string(PARSE_JSON(additional.extended_info),'$$.experiences') AS json) AS experiences,
           CAST(get_json_string(PARSE_JSON(additional.extended_info),'$$.skills') AS json ) AS skills,
           get_json_double(PARSE_JSON(additional.extended_info),'$$.preferredSalaryRange.gte') AS preferred_salary_gte,
           get_json_double(PARSE_JSON(additional.extended_info),'$$.preferredSalaryRange.lte') AS preferred_salary_lte,
           get_json_int(PARSE_JSON(additional.extended_info),'$$.payType') AS payType,
           get_json_double(PARSE_JSON(additional.extended_info),'$$.preferredCurrencyUSDExchangeRate') AS preferred_currency_usd_exchange_rate,
           get_json_int(PARSE_JSON(additional.extended_info),'$$.currency') AS currency,
           CAST(get_json_string(PARSE_JSON(additional.extended_info),'$$.preferredLocations') AS json ) AS preferredLocations,
           get_json_int(PARSE_JSON(additional.extended_info),'$$.preferredPayType') AS preferredPayType,
           additional.current_company AS current_company,
           additional.current_position AS current_position,
           tcl.official_county AS current_location_official_county,
           tcl.official_country AS current_location_official_contry,
           tcl.official_city AS current_location_official_city,
           tcl.official_province AS current_location_official_province,
           tcl.zip_code AS current_location_zip_code,
           tcl.original_loc AS current_location_original_loc,
           tcl.created_by AS current_location_created_by,
           tcl.created_date AS current_location_created_date,
           tcl.last_modified_by AS current_location_last_modified_by,
           tcl.last_modified_date AS current_location_last_modified_date
       FROM ods_apn.talent talent
                LEFT JOIN ods_apn.talent_additional_info additional ON talent.additional_info_id = additional.id
                LEFT JOIN ods_apn.talent_current_location tcl ON talent.id = tcl.talent_id
     """.stripMargin
  }
}
package com.ipg.olap.dwd.table.customizer

import com.ipg.olap.dwd.DwdMetaData
import com.ipg.olap.dwd.DwdTable
import com.ipg.olap.dwd.customizer.DwdTableCustomizer

class StartCustomizer extends DwdTableCustomizer {
  override def customize(table: DwdTable): String = {
    s"""
       CREATE MATERIALIZED VIEW IF NOT EXISTS ${DwdMetaData.DWD_DATABASE}.${table.getName}
       DISTRIBUTED BY HASH(id)
       REFRESH
         IMMEDIATE
         ASYNC EVERY (INTERVAL 60 second)
       PARTITION BY created_date
       AS
       SELECT start.*,
              sa.address                   AS address,
              sa.city                      AS city,
              sa.city_id                   AS city_id,
              sa.province                  AS province,
              sa.country                   AS country,
              sa.zipcode                   AS zipcode,
              sa.location                  AS location,
              sa.created_by                AS address_created_by,
              sa.created_date              AS address_created_date,
              sa.last_modified_by          AS address_last_modified_by,
              sa.last_modified_date        AS address_last_modified_date,
              sa.puser_id                  AS address_puser_id,
              sa.pteam_id                  AS address_pteam_id,
              sa.original_loc              AS original_loc,
              sa.address_line              AS address_line,
              sa.residential_location      AS residential_location,
              sci.client_name              AS client_name,
              sci.client_division          AS client_division,
              sci.client_address           AS client_address,
              sci.client_email             AS client_email,
              sci.created_by               AS client_info_created_by,
              sci.created_date             AS client_info_created_date,
              sci.last_modified_by         AS client_info_last_modified_by,
              sci.last_modified_date       AS client_info_last_modified_date,
              sci.puser_id                 AS client_info_puser_id,
              sci.pteam_id                 AS client_info_pteam_id,
              sci.client_location          AS client_location,
              sci.client_info_id           AS client_info_id,
              sfw.end_date                 AS failed_warranty_end_date,
              sfw.reason                   AS failed_warranty_reason,
              sfw.action_plan              AS failed_warranty_action_plan,
              sfw.total_bill_amount        AS failed_warranty_total_bill_amount,
              sfw.mark_candidate_available AS failed_warranty_mark_candidate_available,
              sfw.note                     AS failed_warranty_note,
              sfw.created_by               AS failed_warranty_created_by,
              sfw.created_date             AS failed_warranty_created_date,
              sfw.last_modified_by         AS failed_warranty_last_modified_by,
              sfw.last_modified_date       AS failed_warranty_last_modified_date,
              sfw.puser_id                 AS failed_warranty_puser_id,
              sfw.pteam_id                 AS failed_warranty_pteam_id,
              sfr.currency                 AS fte_rate_currency,
              sfr.rate_unit_type           AS fte_rate_unit_type,
              sfr.total_billable_amount    AS fte_rate_total_billable_amount,
              sfr.fee_type                 AS fte_rate_fee_type,
              sfr.fee_percentage           AS fte_rate_fee_percentage,
              sfr.total_bill_amount        AS fte_rate_total_bill_amount,
              sfr.created_by               AS fte_rate_created_by,
              sfr.created_date             AS fte_rate_created_date,
              sfr.last_modified_by         AS fte_rate_last_modified_by,
              sfr.last_modified_date       AS fte_rate_last_modified_date,
              sfr.puser_id                 AS fte_rate_puser_id,
              sfr.pteam_id                 AS fte_rate_pteam_id,
              st.start_end_date            AS termination_start_end_date,
              st.termination_date          AS termination_date,
              st.reason                    AS termination_reason,
              st.reason_comments           AS termination_reason_comments,
              st.convert_to_fte_fee_status AS termination_convert_to_fte_fee_status,
              st.note                      AS termination_note,
              st.mark_candidate_available  AS termination_mark_candidate_available,
              st.start_status              AS termination_start_status,
              st.created_by                AS termination_created_by,
              st.created_date              AS termination_created_date,
              st.last_modified_by          AS termination_last_modified_by,
              st.last_modified_date        AS termination_last_modified_date,
              st.puser_id                  AS termination_puser_id,
              st.pteam_id                  AS termination_pteam_id
       FROM ods_apn.start start
                LEFT JOIN ods_apn.start_address sa ON start.id = sa.start_id
                LEFT JOIN ods_apn.start_client_info sci ON start.id = sci.start_id
                LEFT JOIN ods_apn.start_failed_warranty sfw ON start.id = sfw.start_id
                LEFT JOIN ods_apn.start_fte_rate sfr ON start.id = sfr.start_id
                LEFT JOIN ods_apn.start_termination st ON start.id = st.start_id;
     """.stripMargin
  }
}
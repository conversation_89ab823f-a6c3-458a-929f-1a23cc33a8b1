package com.ipg.olap.dwd.table.customizer

import com.ipg.olap.dwd.DwdMetaData
import com.ipg.olap.dwd.DwdTable
import com.ipg.olap.dwd.customizer.DwdTableCustomizer

class JobCustomizer extends DwdTableCustomizer {

  override def customize(table: DwdTable): String = {
    s"""
       CREATE MATERIALIZED VIEW IF NOT EXISTS ${DwdMetaData.DWD_DATABASE}.${table.getName}
       DISTRIBUTED BY HASH(`id`)
       REFRESH
         IMMEDIATE
         ASYNC EVERY (INTERVAL 60 second)
       PARTITION BY created_date
       AS
       SELECT job.*,
       additional.department,
       additional.responsibilities,
       additional.summary,
       additional.requirements,
       additional.public_desc,
       get_json_double(PARSE_JSON(additional.extended_info), '$$.salaryRange.gte') AS salary_gte,
       get_json_double(PARSE_JSON(additional.extended_info), '$$.salaryRange.lte') AS salary_lte,
       get_json_double(PARSE_JSON(additional.extended_info), '$$.currencyUSDExchangeRate') AS currency_usd_exchang_rate,
       CAST(get_json_string(PARSE_JSON(additional.extended_info), '$$.preferredSkills') AS json) AS preferred_skills,
       get_json_string(PARSE_JSON(additional.extended_info), '$$.feeStructure') AS fee_structure,
       get_json_string(PARSE_JSON(additional.extended_info), '$$.contractSigningParty') AS contract_signing_party,
       get_json_string(PARSE_JSON(additional.extended_info), '$$.suggestionsForProspecting') AS suggestions_for_Prospecting,
       get_json_string(PARSE_JSON(additional.extended_info), '$$.recommendedApproach') AS recommended_approach,
       get_json_string(PARSE_JSON(additional.extended_info), '$$.payType') AS pay_type,
       get_json_string(PARSE_JSON(additional.extended_info), '$$.teamComposition') AS team_composition,
       get_json_string(PARSE_JSON(additional.extended_info), '$$.preferredCompanies') AS preferred_companies,
       get_json_double(PARSE_JSON(additional.extended_info), '$$.experienceYearRange.gte') AS experience_year_gte,
       get_json_double(PARSE_JSON(additional.extended_info), '$$.experienceYearRange.lte') AS experience_year_lte,
       CAST(get_json_string(PARSE_JSON(additional.extended_info), '$$.requiredSkills') AS json) AS required_skills,
       get_json_string(PARSE_JSON(additional.extended_info), '$$.reasonForRecruitment') AS reason_for_recruitment,
       get_json_double(PARSE_JSON(additional.extended_info), '$$.estimatedJobFee.fee') AS estimated_job_fee,
       get_json_int(PARSE_JSON(additional.extended_info), '$$.estimatedJobFee.currency') AS estimated_job_currency,
       auto_elimination.auto AS auto_elimination,
       auto_elimination.period AS auto_elimination_period,
       auto_elimination.created_by AS auto_elimination_created_by,
       auto_elimination.created_date AS auto_elimination_created_date,
       auto_elimination.last_modified_by AS auto_elimination_last_modified_by,
       auto_elimination.last_modified_date AS auto_elimination_last_modified_date
    FROM ods_apn.job
         LEFT JOIN ods_apn.job_additional_info AS additional ON ods_apn.job.additional_info_id = additional.id
         LEFT JOIN ods_apn.talent_recruitment_process_auto_elimination AS auto_elimination
                   ON ods_apn.job.id = auto_elimination.job_id
    """.stripMargin
  }
}
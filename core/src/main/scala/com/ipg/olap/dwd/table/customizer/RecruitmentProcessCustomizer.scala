package com.ipg.olap.dwd.table.customizer

import com.ipg.olap.dwd.DwdMetaData
import com.ipg.olap.dwd.DwdTable
import com.ipg.olap.dwd.customizer.DwdTableCustomizer

class RecruitmentProcessCustomizer extends DwdTableCustomizer {
  override def customize(table: DwdTable): String = {
    s"""
       CREATE MATERIALIZED VIEW IF NOT EXISTS ${DwdMetaData.DWD_DATABASE}.${table.getName}
       DISTRIBUTED BY HASH(`id`)
       REFRESH
         IMMEDIATE
         ASYNC
       PARTITION BY created_date
       AS
       SELECT rp.*,
              rpn.id                 AS recruitment_process_node_id,
              rpn.node_type          AS node_type,
              rpn.name               AS node_name,
              rpn.description        AS node_description,
              rpn.next_node_id       AS next_node_id,
              rpn.created_by         AS node_created_by,
              rpn.created_date       AS node_created_date,
              rpn.last_modified_by   AS node_last_modified_by,
              rpn.last_modified_date AS node_last_modified_date
       FROM ods_apn.recruitment_process AS rp
            LEFT JOIN ods_apn.recruitment_process_node rpn ON rp.id = rpn.recruitment_process_id;
     """.stripMargin
  }
}
package com.ipg.olap.dwd.table.customizer

import com.ipg.olap.dwd.DwdMetaData
import com.ipg.olap.dwd.DwdTable
import com.ipg.olap.dwd.customizer.DwdTableCustomizer
import com.ipg.olap.ods.OdsMetaData

class NormalViewCustomizer extends DwdTableCustomizer {

  override def customize(table: DwdTable): String = {
    if (table == null || table.getName == null || table.getOdsTableName == null) {
      throw new IllegalArgumentException("Table 或其必要属性为空")
    }

    s"CREATE VIEW IF NOT EXISTS ${DwdMetaData.DWD_DATABASE}.${table.getName} AS SELECT * FROM ${OdsMetaData.odsDataBaseName}.${table.getOdsTableName}"
  }
}
package com.ipg.olap.dwd.table.customizer

import com.ipg.olap.dwd.customizer.DwdTableCustomizer
import com.ipg.olap.dwd.{DwdMetaData, DwdTable}

class ApplicationOnboardCustomizer extends DwdTableCustomizer{

  override def customize(table: DwdTable): String = {
    s"""
    CREATE VIEW IF NOT EXISTS ${DwdMetaData.DWD_DATABASE}.${table.getName} AS
    SELECT treo.*,
           treowl.official_country AS work_official_contry,
           treowl.official_province AS work_location_official_province,
           treowl.official_city     AS work_location_official_city,
           treowl.official_county   AS work_location_official_county,
           treowl.original_loc AS work_original_loc,
           treowl.address_line AS work_address_line,
           treowl.residential_location AS work_residential_location
    FROM ods_apn.talent_recruitment_process_onboard treo
    LEFT JOIN ods_apn.talent_recruitment_process_onboard_work_location treowl
                ON treo.talent_recruitment_process_id = treowl.talent_recruitment_process_id;
    """.stripMargin
  }
}

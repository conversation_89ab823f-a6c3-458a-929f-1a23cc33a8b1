package com.ipg.olap.dwd.table.customizer

import com.ipg.olap.dwd.DwdMetaData
import com.ipg.olap.dwd.DwdTable
import com.ipg.olap.dwd.customizer.DwdTableCustomizer

class UserCustomizer extends DwdTableCustomizer {
  override def customize(table: DwdTable): String = {
    s"""
       CREATE MATERIALIZED VIEW IF NOT EXISTS ${DwdMetaData.DWD_DATABASE}.${table.getName}
       DISTRIBUTED BY HASH(id)
       REFRESH
         IMMEDIATE
         ASYNC EVERY (INTERVAL 60 second)
       PARTITION BY created_date
       AS
       SELECT user.*,
              ua.account_type,
              ua.amount,
              ua.bulk_credit        AS account_bulk_credit,
              ua.frozen_amount,
              ua.total_amount,
              ua.account_status,
              ua.expire_date,
              ua.version,
              ua.credit_effect_type,
              ua.effect_credit,
              ua.created_by         AS account_created_by,
              ua.created_date       AS account_created_date,
              ua.last_modified_by   AS account_last_modified_by,
              ua.last_modified_date AS account_last_modified_date,
              ucr.crm_user_id
       FROM ods_apn.user AS user
                LEFT JOIN ods_apn.user_account AS ua on user.id = ua.user_id
                LEFT JOIN ods_apn.user_crm_relation AS ucr on user.id = ucr.user_id
     """.stripMargin
  }
}
package com.ipg.olap;

import org.apache.flink.cdc.common.pipeline.SchemaChangeBehavior;

public class Env {

    public static String mysqlHost() {
        return System.getenv("MYSQL_HOST");
    }

    public static Integer mysqlPort() {
        String port = System.getenv("MYSQL_PORT");
        if (port == null) {
            return 3306;
        }
        return Integer.valueOf(port);
    }

    public static String mysqlUser() {
        return System.getenv("MYSQL_USER");
    }

    public static String mysqlPassword() {
        return System.getenv("MYSQL_PASSWORD");
    }

    public static String starRocksJdbcUrl() {
        return System.getenv("STARROCKS_JDBC_URL");
    }

    public static String starRocksLoadUrl() {
        return System.getenv("STARROCKS_LOAD_URL");
    }

    public static String starRocksUser() {
        return System.getenv("STARROCKS_USER");
    }

    public static String starRocksPassword() {
        return System.getenv("STARROCKS_PASSWORD");
    }

    public static Integer starrocksBeNums() {
        return System.getenv("STARROCKS_BE_NUMS") == null ? 3 : Integer.parseInt(System.getenv("STARROCKS_BE_NUMS"));
    }

    public static int checkPointInterval() {
       String interval = System.getenv("CHECKPOINT_INTERVAL");
       if (interval == null) {
           return 60000;
       }
       return Integer.parseInt(interval);
    }

    public static int parallelism() {
        String parallelism = System.getenv("PARALLELISM");
        if (parallelism == null) {
            return 1;
        }
        return Integer.parseInt(parallelism);
    }

    public static String schemaChangeBehavior() {
        String behavior = System.getenv("SCHEMA_CHANGE_BEHAVIOR");
        if (behavior == null) {
            return SchemaChangeBehavior.TRY_EVOLVE.name();
        }
        return behavior;
    }
}

package com.ipg.olap.dwd;

import com.ipg.olap.dwd.customizer.DwdTableCustomizer;
import com.ipg.olap.dwd.table.customizer.NormalViewCustomizer;

import java.util.function.Supplier;

public class DwdTable {

    private static final String PREFIX = "dwd_";

    private String odsTableName;
    private String name;
    private ViewType viewType;
    private TableCategory tableCategory;
    private Supplier<DwdTableCustomizer> customizer;

    public DwdTable(String odsTableName, TableCategory tableCategory) {
        this.odsTableName = odsTableName;
        this.name = PREFIX + odsTableName;
        this.tableCategory = tableCategory;
        this.viewType = ViewType.NORMAL;
        this.customizer = NormalViewCustomizer::new;
    }
    public DwdTable(String odsTableName, ViewType viewType, TableCategory tableCategory, Supplier<DwdTableCustomizer> customizer) {
        this.odsTableName = PREFIX + odsTableName;
        this.name = PREFIX + odsTableName;
        this.viewType = viewType;
        this.tableCategory = tableCategory;
        this.customizer = customizer;
    }

    public String getOdsTableName() {
        return odsTableName;
    }

    public void setOdsTableName(String odsTableName) {
        this.odsTableName = odsTableName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public ViewType getViewType() {
        return viewType;
    }

    public void setViewType(ViewType viewType) {
        this.viewType = viewType;
    }

    public TableCategory getTableCategory() {
        return tableCategory;
    }

    public void setTableCategory(TableCategory tableCategory) {
        this.tableCategory = tableCategory;
    }

    public Supplier<DwdTableCustomizer> getCustomizer() {
        return customizer;
    }

    public void setCustomizer(Supplier<DwdTableCustomizer> customizer) {
        this.customizer = customizer;
    }
}

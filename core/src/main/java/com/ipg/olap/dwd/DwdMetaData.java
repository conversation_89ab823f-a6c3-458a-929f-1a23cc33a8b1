package com.ipg.olap.dwd;

import com.google.common.collect.ImmutableList;
import com.ipg.olap.dwd.table.customizer.*;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ipg.olap.dwd.TableCategory.DIMENSION;
import static com.ipg.olap.dwd.TableCategory.FACT;
import static com.ipg.olap.dwd.ViewType.MATERIALIZED;


public interface DwdMetaData {

    String DWD_DATABASE = "dwd_apn";

    /* Assignment Detail Tables */
    DwdTable ASSIGNMENT_BILL_INFO_TABLE = new DwdTable("assignment_bill_info", FACT);
    DwdTable ASSIGNMENT_CONTRIBUTION_TABLE = new DwdTable("assignment_contribution", FACT);
    DwdTable ASSIGNMENT_LOCATION_TABLE = new DwdTable("assignment_location", FACT);
    DwdTable ASSIGNMENT_PAY_INFO_TABLE = new DwdTable("assignment_pay_info", FACT);
    DwdTable ASSIGNMENT_PAY_RATE_TABLE = new DwdTable("assignment_pay_rate", FACT);
    DwdTable ASSIGNMENT_TIMESHEET_TABLE = new DwdTable("assignment_timesheet", FACT);
    List<DwdTable> assignmentDetailTables = ImmutableList.of(
        ASSIGNMENT_BILL_INFO_TABLE,
        ASSIGNMENT_CONTRIBUTION_TABLE,
        ASSIGNMENT_LOCATION_TABLE,
        ASSIGNMENT_PAY_INFO_TABLE,
        ASSIGNMENT_PAY_RATE_TABLE,
        ASSIGNMENT_TIMESHEET_TABLE
    );


    /* Company Tables */
    DwdTable COMPANY_TABLE = new DwdTable("company", MATERIALIZED, DIMENSION, CompanyCustomizer::new);
    DwdTable COMPANY_LOCATION_TABLE = new DwdTable("company_location", DIMENSION);
    DwdTable COMPANY_INDUSTRY_RELATION_TABLE = new DwdTable("company_industry_relation", FACT);
    DwdTable COMPANY_PROJECT_TEAM_TABLE = new DwdTable("company_project_team", DIMENSION);
    DwdTable COMPANY_PROJECT_TEAM_USER_TABLE = new DwdTable("company_project_team_user", FACT);
    DwdTable COMPANY_SALES_LEAD_CLIENT_CONTACT_TABLE = new DwdTable("company_sales_lead_client_contact", FACT);
    DwdTable COMPANY_USER_RELATION_TABLE = new DwdTable("company_user_relation", FACT);
    List<DwdTable> companyTables = ImmutableList.of(
        COMPANY_TABLE,
        COMPANY_LOCATION_TABLE,
        COMPANY_INDUSTRY_RELATION_TABLE,
        COMPANY_PROJECT_TEAM_TABLE,
        COMPANY_PROJECT_TEAM_USER_TABLE,
        COMPANY_SALES_LEAD_CLIENT_CONTACT_TABLE,
        COMPANY_USER_RELATION_TABLE
    );


    /* Currency Tables */
    DwdTable CURRENCY_RATE_DAY_TABLE = new DwdTable("currency_rate_day", FACT);
    DwdTable ENUM_CURRENCY_TABLE = new DwdTable("enum_currency", DIMENSION);
    List<DwdTable> currencyTables = ImmutableList.of(
        CURRENCY_RATE_DAY_TABLE,
        ENUM_CURRENCY_TABLE
    );

    /* Invoice Tables */
    DwdTable INVOICE_TABLE = new DwdTable("invoice", FACT);
    DwdTable INVOICE_ACTIVITY_TABLE = new DwdTable("invoice_activity", FACT);
    DwdTable INVOICE_CLIENT_CREDIT_TABLE = new DwdTable("invoice_client_credit", DIMENSION);
    DwdTable INVOICE_PAYMENT_RECORD_TABLE = new DwdTable("invoice_payment_record", FACT);
    List<DwdTable> invoiceTables = ImmutableList.of(
        INVOICE_TABLE,
        INVOICE_ACTIVITY_TABLE,
        INVOICE_CLIENT_CREDIT_TABLE,
        INVOICE_PAYMENT_RECORD_TABLE
    );

    DwdTable JOB_TABLE = new DwdTable("job", MATERIALIZED, DIMENSION, JobCustomizer::new);
    DwdTable JOB_COMPANY_CONTACT_RELATION_TABLE = new DwdTable("job_company_contact_relation", FACT);
    DwdTable JOB_JOB_FUNCTION_RELATION_TABLE = new DwdTable("job_job_function_relation", FACT);
    DwdTable JOB_LOCATION_TABLE = new DwdTable("job_location", FACT);
    DwdTable JOB_USER_RELATION_TABLE = new DwdTable("job_user_relation", FACT);
    List<DwdTable> jobTables = ImmutableList.of(
        JOB_TABLE,
        JOB_COMPANY_CONTACT_RELATION_TABLE,
        JOB_JOB_FUNCTION_RELATION_TABLE,
        JOB_LOCATION_TABLE,
        JOB_USER_RELATION_TABLE
    );

    /* Permission Tables */
    DwdTable PERMISSION_EXTRA_ROLE_TEAM_TABLE = new DwdTable("permission_extra_role_team", FACT);
    DwdTable PERMISSION_EXTRA_USER_TEAM_TABLE = new DwdTable("permission_extra_user_team", FACT);
    DwdTable PERMISSION_TEAM_TABLE = new DwdTable("permission_team", DIMENSION);
    DwdTable PERMISSION_USER_TEAM_TABLE = new DwdTable("permission_user_team", FACT);
    List<DwdTable> permissionTables = ImmutableList.of(
        PERMISSION_EXTRA_ROLE_TEAM_TABLE,
        PERMISSION_EXTRA_USER_TEAM_TABLE,
        PERMISSION_TEAM_TABLE,
        PERMISSION_USER_TEAM_TABLE
    );

    /* Recruitment Tables */
    DwdTable RECRUITMENT_PROCESS_TABLE = new DwdTable("recruitment_process", MATERIALIZED, DIMENSION, RecruitmentProcessCustomizer::new);
    List<DwdTable> recruitmentTables = ImmutableList.of(
        RECRUITMENT_PROCESS_TABLE
    );

    /* Resume Tables */
    DwdTable RESUME_TABLE = new DwdTable("resume", FACT);
    List<DwdTable> resumeTables = ImmutableList.of(
        RESUME_TABLE
    );

    /* Role Tables */
    DwdTable ROLE_TABLE = new DwdTable("role", DIMENSION);
    List<DwdTable> roleTables = ImmutableList.of(
        ROLE_TABLE
    );

    /* Start Tables */
    DwdTable START_TABLE = new DwdTable("start", MATERIALIZED, FACT, StartCustomizer::new);
    DwdTable START_COMMISSION_TABLE = new DwdTable("start_commission", FACT);
    DwdTable START_CONTRACT_RATE_TABLE = new DwdTable("start_contract_rate", FACT);
    DwdTable START_FTE_SALARY_PACKAGE_TABLE = new DwdTable("start_fte_salary_package", FACT);
    DwdTable START_RATE_CHANGE_TABLE = new DwdTable("start_rate_change", FACT);
    DwdTable START_RESIDENTIAL_ADDRESS_TABLE = new DwdTable("start_residential_address", FACT);
    List<DwdTable> startTables = ImmutableList.of(
        START_TABLE,
        START_COMMISSION_TABLE,
        START_CONTRACT_RATE_TABLE,
        START_FTE_SALARY_PACKAGE_TABLE,
        START_RATE_CHANGE_TABLE,
        START_RESIDENTIAL_ADDRESS_TABLE
    );

    /* Contractor Invoice Tables */
    DwdTable CONTRACTOR_INVOICE_TABLE = new DwdTable("t_contractor_invoice", FACT);
    DwdTable EMAIL_ATTACHMENT_RECORD_TABLE = new DwdTable("t_email_attachment_record", FACT);
    DwdTable GROUP_INVOICE_TABLE = new DwdTable("t_group_invoice", FACT);
    DwdTable GROUP_INVOICE_RECORD_TABLE = new DwdTable("t_group_invoice_record", FACT);
    DwdTable INVOICE_EXPENSE_INFO_TABLE = new DwdTable("t_invoice_expense_info", FACT);
    DwdTable INVOICE_TIMESHEET_INFO_TABLE = new DwdTable("t_invoice_timesheet_info", FACT);
    DwdTable RECORD_PAYMENT_INFO_TABLE = new DwdTable("t_record_payment_info", FACT);
    List<DwdTable> contractorInvoiceTables = ImmutableList.of(
        CONTRACTOR_INVOICE_TABLE,
        EMAIL_ATTACHMENT_RECORD_TABLE,
        GROUP_INVOICE_TABLE,
        GROUP_INVOICE_RECORD_TABLE,
        INVOICE_EXPENSE_INFO_TABLE,
        INVOICE_TIMESHEET_INFO_TABLE,
        RECORD_PAYMENT_INFO_TABLE
    );

    /* Talent Tables */
    DwdTable TALENT_TABLE = new DwdTable("talent", MATERIALIZED, DIMENSION, TalentCustomizer::new);
    DwdTable TALENT_CONTACT_TABLE = new DwdTable("talent_contact", FACT);
    DwdTable TALENT_INDUSTRY_RELATION_TABLE = new DwdTable("talent_industry_relation", FACT);
    DwdTable TALENT_JOB_FUNCTION_RELATION_TABLE = new DwdTable("talent_job_function_relation", FACT);
    DwdTable TALENT_LANGUAGE_RELATION_TABLE = new DwdTable("talent_language_relation", FACT);
    DwdTable TALENT_OWNERSHIP_TABLE = new DwdTable("talent_ownership", FACT);
    DwdTable TALENT_RESUME_RELATION_TABLE = new DwdTable("talent_resume_relation", FACT);
    DwdTable TALENT_USER_RELATION_TABLE = new DwdTable("talent_user_relation", FACT);
    DwdTable TALENT_WORK_AUTHORIZATION_RELATION_TABLE = new DwdTable("talent_work_authorization_relation", FACT);
    List<DwdTable> talentTables = ImmutableList.of(
        TALENT_TABLE,
        TALENT_CONTACT_TABLE,
        TALENT_INDUSTRY_RELATION_TABLE,
        TALENT_JOB_FUNCTION_RELATION_TABLE,
        TALENT_LANGUAGE_RELATION_TABLE,
        TALENT_OWNERSHIP_TABLE,
        TALENT_RESUME_RELATION_TABLE,
        TALENT_USER_RELATION_TABLE,
        TALENT_WORK_AUTHORIZATION_RELATION_TABLE
    );

    /* Application Tables */
    DwdTable TALENT_RECRUITMENT_PROCESS_TABLE = new DwdTable("talent_recruitment_process", FACT);
    DwdTable TALENT_RECRUITMENT_PROCESS_COMMISSION_TABLE = new DwdTable("talent_recruitment_process_commission", FACT);
    DwdTable TALENT_RECRUITMENT_PROCESS_ELIMINATE_TABLE = new DwdTable("talent_recruitment_process_eliminate", FACT);
    DwdTable TALENT_RECRUITMENT_PROCESS_INTERVIEW_TABLE = new DwdTable("talent_recruitment_process_interview", FACT);
    DwdTable TALENT_RECRUITMENT_PROCESS_IPG_AGREED_PAY_RATE_TABLE = new DwdTable("talent_recruitment_process_ipg_agreed_pay_rate", FACT);
    DwdTable TALENT_RECRUITMENT_PROCESS_IPG_CONTRACT_FEE_CHARGE_TABLE = new DwdTable("talent_recruitment_process_ipg_contract_fee_charge", FACT);
    DwdTable TALENT_RECRUITMENT_PROCESS_IPG_OFFER_ACCEPT_TABLE = new DwdTable("talent_recruitment_process_ipg_offer_accept", FACT);
    DwdTable TALENT_RECRUITMENT_PROCESS_IPG_OFFER_LETTER_COST_RATE_TABLE = new DwdTable("talent_recruitment_process_ipg_offer_letter_cost_rate", FACT);
    DwdTable TALENT_RECRUITMENT_PROCESS_KPI_USER_TABLE = new DwdTable("talent_recruitment_process_kpi_user", FACT);
    DwdTable TALENT_RECRUITMENT_PROCESS_NODE_TABLE = new DwdTable("talent_recruitment_process_node", FACT);
    DwdTable TALENT_RECRUITMENT_PROCESS_OFFER_TABLE = new DwdTable("talent_recruitment_process_offer", FACT);
    DwdTable TALENT_RECRUITMENT_PROCESS_OFFER_FEE_CHARGE_TABLE = new DwdTable("talent_recruitment_process_offer_fee_charge", FACT);
    DwdTable TALENT_RECRUITMENT_PROCESS_OFFER_SALARY_PACKAGE_TABLE = new DwdTable("talent_recruitment_process_offer_salary_package", FACT);
    DwdTable TALENT_RECRUITMENT_PROCESS_ONBOARD_TABLE = new DwdTable("talent_recruitment_process_onboard", ViewType.NORMAL, FACT, ApplicationOnboardCustomizer::new);
    DwdTable TALENT_RECRUITMENT_PROCESS_ONBOARD_CLIENT_INFO_TABLE = new DwdTable("talent_recruitment_process_onboard_client_info", FACT);
    DwdTable TALENT_RECRUITMENT_PROCESS_ONBOARD_DATE_TABLE = new DwdTable("talent_recruitment_process_onboard_date", FACT);
    DwdTable TALENT_RECRUITMENT_PROCESS_RESIGNATION_TABLE = new DwdTable("talent_recruitment_process_resignation", FACT);
    DwdTable TALENT_RECRUITMENT_PROCESS_STOP_STATISTICS_TABLE = new DwdTable("talent_recruitment_process_stop_statistics", FACT);
    DwdTable TALENT_RECRUITMENT_PROCESS_SUBMIT_TO_CLIENT_TABLE = new DwdTable("talent_recruitment_process_submit_to_client", FACT);
    DwdTable TALENT_RECRUITMENT_PROCESS_SUBMIT_TO_JOB_TABLE = new DwdTable("talent_recruitment_process_submit_to_job", FACT);

    List<DwdTable> talentRecruitmentProcessTables = ImmutableList.of(
        TALENT_RECRUITMENT_PROCESS_TABLE,
        TALENT_RECRUITMENT_PROCESS_COMMISSION_TABLE,
        TALENT_RECRUITMENT_PROCESS_ELIMINATE_TABLE,
        TALENT_RECRUITMENT_PROCESS_INTERVIEW_TABLE,
        TALENT_RECRUITMENT_PROCESS_IPG_AGREED_PAY_RATE_TABLE,
        TALENT_RECRUITMENT_PROCESS_IPG_CONTRACT_FEE_CHARGE_TABLE,
        TALENT_RECRUITMENT_PROCESS_IPG_OFFER_ACCEPT_TABLE,
        TALENT_RECRUITMENT_PROCESS_IPG_OFFER_LETTER_COST_RATE_TABLE,
        TALENT_RECRUITMENT_PROCESS_KPI_USER_TABLE,
        TALENT_RECRUITMENT_PROCESS_NODE_TABLE,
        TALENT_RECRUITMENT_PROCESS_OFFER_TABLE,
        TALENT_RECRUITMENT_PROCESS_OFFER_FEE_CHARGE_TABLE,
        TALENT_RECRUITMENT_PROCESS_OFFER_SALARY_PACKAGE_TABLE,
        TALENT_RECRUITMENT_PROCESS_ONBOARD_TABLE,
        TALENT_RECRUITMENT_PROCESS_ONBOARD_CLIENT_INFO_TABLE,
        TALENT_RECRUITMENT_PROCESS_ONBOARD_DATE_TABLE,
        TALENT_RECRUITMENT_PROCESS_RESIGNATION_TABLE,
        TALENT_RECRUITMENT_PROCESS_STOP_STATISTICS_TABLE,
        TALENT_RECRUITMENT_PROCESS_SUBMIT_TO_CLIENT_TABLE,
        TALENT_RECRUITMENT_PROCESS_SUBMIT_TO_JOB_TABLE
    );

    /* Tenant Tables */
    DwdTable TENANT_TABLE = new DwdTable("tenant", DIMENSION);
    List<DwdTable> tenantTables = ImmutableList.of(
        TENANT_TABLE
    );

    /* Timesheet Tables */
    DwdTable TIME_SHEET_EXPENSE_WEEK_ENDING_RECORD_TABLE = new DwdTable("time_sheet_expense_week_ending_record", FACT);
    DwdTable TIME_SHEET_HOLIDAY_RECORD_TABLE = new DwdTable("time_sheet_holiday_record", FACT);
    DwdTable TIME_SHEET_RECORD_TABLE = new DwdTable("time_sheet_record", FACT);
    DwdTable TIME_SHEET_USER_TABLE = new DwdTable("time_sheet_user", FACT);
    DwdTable TIME_SHEET_WEEK_ENDING_RECORD_TABLE = new DwdTable("time_sheet_week_ending_record", FACT);
    DwdTable TIMESHEET_APPROVE_RECORD_TABLE = new DwdTable("timesheet_approve_record", FACT);
    DwdTable TIMESHEET_BREAKTIME_RECORD_TABLE = new DwdTable("timesheet_breaktime_record", FACT);
    DwdTable TIMESHEET_CALCULATE_STATE_TABLE = new DwdTable("timesheet_calculate_state", FACT);
    DwdTable TIMESHEET_COMMENTS_TABLE = new DwdTable("timesheet_comments", FACT);
    DwdTable TIMESHEET_EXPENSE_RECORD_TABLE = new DwdTable("timesheet_expense_record", FACT);
    DwdTable TIMESHEET_GOOGLE_HOLIDAY_TABLE = new DwdTable("timesheet_google_holiday", FACT);
    DwdTable TIMESHEET_MANAGER_TABLE = new DwdTable("timesheet_manager", FACT);
    DwdTable TIMESHEET_TALENT_ASSIGNMENT_TABLE = new DwdTable("timesheet_talent_assignment", FACT);
    List<DwdTable> timesheetTables = ImmutableList.of(
        TIME_SHEET_EXPENSE_WEEK_ENDING_RECORD_TABLE,
        TIME_SHEET_HOLIDAY_RECORD_TABLE,
        TIME_SHEET_RECORD_TABLE,
        TIME_SHEET_USER_TABLE,
        TIME_SHEET_WEEK_ENDING_RECORD_TABLE,
        TIMESHEET_APPROVE_RECORD_TABLE,
        TIMESHEET_BREAKTIME_RECORD_TABLE,
        TIMESHEET_CALCULATE_STATE_TABLE,
        TIMESHEET_COMMENTS_TABLE,
        TIMESHEET_EXPENSE_RECORD_TABLE,
        TIMESHEET_GOOGLE_HOLIDAY_TABLE,
        TIMESHEET_MANAGER_TABLE,
        TIMESHEET_TALENT_ASSIGNMENT_TABLE
    );

    /* User Tables */
    DwdTable USER_TABLE = new DwdTable("user", MATERIALIZED, DIMENSION, UserCustomizer::new);
    DwdTable USER_JOB_RELATION_TABLE = new DwdTable("user_job_relation", FACT);
    DwdTable USER_ROLE_TABLE = new DwdTable("user_role", FACT);
    List<DwdTable> userTables = ImmutableList.of(
        USER_TABLE,
        USER_JOB_RELATION_TABLE,
        USER_ROLE_TABLE
    );

    /* Enum Tables */
    List<DwdTable> enumTables = Stream.of(
        "enum_business_progress",
        "enum_company_client_level",
        "enum_company_contact_category",
        "enum_company_contact_tag",
        "enum_company_sale_lead_source",
        "enum_company_service_type",
        "enum_company_tag",
        "enum_contact_type",
//        "enum_country",
        "enum_currency",
//        "enum_degree",
        "enum_follow_up_contact_type",
        "enum_gender",
//        "enum_industry",
        "enum_invoicing_service_tax",
        "enum_job_priority",
        "enum_language",
        "enum_motivation",
        "enum_receiving_account",
        "enum_timezone",
        "enum_work_authorization"
    ).map(odsTableName -> new DwdTable(odsTableName, DIMENSION)).collect(Collectors.toList());

    List<DwdTable> DWD_TABLES = Stream.of(
            assignmentDetailTables,
            companyTables,
            currencyTables,
            invoiceTables,
            jobTables,
            permissionTables,
            recruitmentTables,
            resumeTables,
            roleTables,
            startTables,
            contractorInvoiceTables,
            talentTables,
            talentRecruitmentProcessTables,
            tenantTables,
            timesheetTables,
            userTables,
            enumTables
        )
        .flatMap(List::stream)
        .collect(Collectors.toList());
}

package com.ipg.olap.ods.customizer;

import com.google.common.collect.ImmutableList;
import com.ipg.olap.scmema.StarRocksTable;

import java.util.List;

import static com.ipg.olap.ods.customizer.Constants.CREATED_DATE;
import static com.ipg.olap.ods.customizer.Constants.ID;

/**
 * 通用表自定义规则
 * 1. 主键：ID, CREATED_DATE
 * 2. 分区：按月分区，分区字段为CREATED_DATE
 * 如果没有CREATED_DATE字段，则不进行自定义
 */
public class CommonCustomizer implements TableCustomizer {

    @Override
    public StarRocksTable customize(StarRocksTable table) {
        if (table.getColumn(CREATED_DATE) == null) {
            return table;
        }
        List<String> keys = ImmutableList.of(ID, CREATED_DATE);
        StarRocksTable.Builder builder = table.toBuilder();
        return builder.setTableKeys(keys)
            .setDistributionKeys(ImmutableList.of(ID))
            .setPartitionExpress(String.format("date_trunc('month', `%s`)", CREATED_DATE))
            .setColumns(ColumnSorter.sortColumns(table.getColumns(), keys))
            .build().setCustomized(true);
    }

    @Override
    public boolean isCustomizable(String tableName) {
        return true;
    }
}

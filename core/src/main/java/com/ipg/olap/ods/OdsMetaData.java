package com.ipg.olap.ods;

import com.google.common.collect.ImmutableList;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public interface OdsMetaData {

    String odsDataBaseName = "ods_apn";
    String sourceDataBaseName = "apnv3";

    /* Assignment Tables */
    String ASSIGNMENT_BILL_INFO_TABLE = "assignment_bill_info";
    String ASSIGNMENT_CONTRIBUTION_TABLE = "assignment_contribution";
    String ASSIGNMENT_LOCATION_TABLE = "assignment_location";
    String ASSIGNMENT_PAY_INFO_TABLE = "assignment_pay_info";
    String ASSIGNMENT_PAY_RATE_TABLE = "assignment_pay_rate";
    String ASSIGNMENT_TIMESHEET_TABLE = "assignment_timesheet";
    List<String> assignmentDetailTables = ImmutableList.of(
        ASSIGNMENT_BILL_INFO_TABLE,
        ASSIGNMENT_CONTRIBUTION_TABLE,
        ASSIGNMENT_LOCATION_TABLE,
        ASSIGNMENT_PAY_INFO_TABLE,
        ASSIGNMENT_PAY_RATE_TABLE,
        ASSIGNMENT_TIMESHEET_TABLE
    );

    /* Company Tables */
    String COMPANY_TABLE = "company";
    String ACCOUNT_BUSINESS = "account_business";
    String ACCOUNT_BUSINESS_SERVICE_TYPE_RELATION = "account_business_service_type_relation";
    String COMPANY_ADDITIONAL_INFO_TABLE = "company_additional_info";
    String COMPANY_INDUSTRY_RELATION_TABLE = "company_industry_relation";
    String COMPANY_LOCATION_TABLE = "company_location";
    String COMPANY_PROJECT_TEAM_TABLE = "company_project_team";
    String COMPANY_PROJECT_TEAM_USER_TABLE = "company_project_team_user";
    String COMPANY_SALES_LEAD_CLIENT_CONTACT_TABLE = "company_sales_lead_client_contact";
    String COMPANY_USER_RELATION_TABLE = "company_user_relation";
    String COMPANY_BUSINESS_FLOW_ADMINISTRATOR_TABLE = "business_flow_administrator";
    String COMPANY_CLIENT_NOTE = "company_client_note";
    List<String> companyTables = ImmutableList.of(
        COMPANY_TABLE,
        ACCOUNT_BUSINESS,
        ACCOUNT_BUSINESS_SERVICE_TYPE_RELATION,
        COMPANY_ADDITIONAL_INFO_TABLE,
        COMPANY_INDUSTRY_RELATION_TABLE,
        COMPANY_LOCATION_TABLE,
        COMPANY_PROJECT_TEAM_TABLE,
        COMPANY_PROJECT_TEAM_USER_TABLE,
        COMPANY_SALES_LEAD_CLIENT_CONTACT_TABLE,
        COMPANY_USER_RELATION_TABLE,
        COMPANY_BUSINESS_FLOW_ADMINISTRATOR_TABLE,
        COMPANY_CLIENT_NOTE
    );

    /* Currency Tables */
    String CURRENCY_RATE_DAY_TABLE = "currency_rate_day";
    String ENUM_CURRENCY_TABLE = "enum_currency";
    List<String> currencyTables = ImmutableList.of(
        CURRENCY_RATE_DAY_TABLE,
        ENUM_CURRENCY_TABLE
    );

    /* Invoice Tables */
    String INVOICE_TABLE = "invoice";
    String INVOICE_ACTIVITY_TABLE = "invoice_activity";
    String INVOICE_CLIENT_CREDIT_TABLE = "invoice_client_credit";
    String INVOICE_PAYMENT_RECORD_TABLE = "invoice_payment_record";
    List<String> invoiceTables = ImmutableList.of(
        INVOICE_TABLE,
        INVOICE_ACTIVITY_TABLE,
        INVOICE_CLIENT_CREDIT_TABLE,
        INVOICE_PAYMENT_RECORD_TABLE
    );

    String JOB_TABLE = "job";
    String JOB_NOTE = "job_note";
    String JOB_ADDITIONAL_INFO_TABLE = "job_additional_info";
    String JOB_COMPANY_CONTACT_RELATION_TABLE = "job_company_contact_relation";
    String TALENT_RECRUITMENT_PROCESS_AUTO_ELIMINATION_TABLE = "talent_recruitment_process_auto_elimination";
    String JOB_JOB_FUNCTION_RELATION_TABLE = "job_job_function_relation";
    String JOB_LOCATION_TABLE = "job_location";
    String JOB_USER_RELATION_TABLE = "job_user_relation";
    String JOB_TALENT_RECOMMEND_FEEDBACK = "job_talent_recommend_feedback";
    List<String> jobTables = ImmutableList.of(
        JOB_TABLE,
        JOB_NOTE,
        JOB_ADDITIONAL_INFO_TABLE,
        JOB_COMPANY_CONTACT_RELATION_TABLE,
        JOB_JOB_FUNCTION_RELATION_TABLE,
        JOB_LOCATION_TABLE,
        JOB_USER_RELATION_TABLE,
        TALENT_RECRUITMENT_PROCESS_AUTO_ELIMINATION_TABLE,
        JOB_TALENT_RECOMMEND_FEEDBACK
    );

    /* Permission Tables */
    String PERMISSION_EXTRA_ROLE_TEAM_TABLE = "permission_extra_role_team";
    String PERMISSION_EXTRA_USER_TEAM_TABLE = "permission_extra_user_team";
    String PERMISSION_TEAM_TABLE = "permission_team";
    String PERMISSION_USER_TEAM_TABLE = "permission_user_team";
    List<String> permissionTables = ImmutableList.of(
        PERMISSION_EXTRA_ROLE_TEAM_TABLE,
        PERMISSION_EXTRA_USER_TEAM_TABLE,
        PERMISSION_TEAM_TABLE,
        PERMISSION_USER_TEAM_TABLE
    );

    /* Recruitment Tables */
    String RECRUITMENT_PROCESS_TABLE = "recruitment_process";
    String RECRUITMENT_PROCESS_NODE_TABLE = "recruitment_process_node";
    List<String> recruitmentTables = ImmutableList.of(
        RECRUITMENT_PROCESS_TABLE,
        RECRUITMENT_PROCESS_NODE_TABLE
    );

    /* Resume Tables */
    String RESUME_TABLE = "resume";
    List<String> resumeTables = ImmutableList.of(
        RESUME_TABLE
    );

    /* Role Tables */
    String ROLE_TABLE = "role";
    List<String> roleTables = ImmutableList.of(
        ROLE_TABLE
    );

    /* Start Tables */
    String START_TABLE = "start";
    String START_ADDRESS_TABLE = "start_address";
    String START_CLIENT_INFO_TABLE = "start_client_info";
    String START_COMMISSION_TABLE = "start_commission";
    String START_CONTRACT_RATE_TABLE = "start_contract_rate";
    String START_FAILED_WARRANTY_TABLE = "start_failed_warranty";
    String START_FTE_RATE_TABLE = "start_fte_rate";
    String START_FTE_SALARY_PACKAGE_TABLE = "start_fte_salary_package";
    String START_RATE_CHANGE_TABLE = "start_rate_change";
    String START_RESIDENTIAL_ADDRESS_TABLE = "start_residential_address";
    String START_TERMINATION_TABLE = "start_termination";
    List<String> startTables = ImmutableList.of(
        START_TABLE,
        START_ADDRESS_TABLE,
        START_CLIENT_INFO_TABLE,
        START_COMMISSION_TABLE,
        START_CONTRACT_RATE_TABLE,
        START_FAILED_WARRANTY_TABLE,
        START_FTE_RATE_TABLE,
        START_FTE_SALARY_PACKAGE_TABLE,
        START_RATE_CHANGE_TABLE,
        START_RESIDENTIAL_ADDRESS_TABLE,
        START_TERMINATION_TABLE
    );

    /* Contractor Invoice Tables */
    String CONTRACTOR_INVOICE_TABLE = "t_contractor_invoice";
    String EMAIL_ATTACHMENT_RECORD_TABLE = "t_email_attachment_record";
    String GROUP_INVOICE_TABLE = "t_group_invoice";
    String GROUP_INVOICE_RECORD_TABLE = "t_group_invoice_record";
    String INVOICE_EXPENSE_INFO_TABLE = "t_invoice_expense_info";
    String INVOICE_TIMESHEET_INFO_TABLE = "t_invoice_timesheet_info";
    String RECORD_PAYMENT_INFO_TABLE = "t_record_payment_info";
    List<String> contractorInvoiceTables = ImmutableList.of(
        CONTRACTOR_INVOICE_TABLE,
        EMAIL_ATTACHMENT_RECORD_TABLE,
        GROUP_INVOICE_TABLE,
        GROUP_INVOICE_RECORD_TABLE,
        INVOICE_EXPENSE_INFO_TABLE,
        INVOICE_TIMESHEET_INFO_TABLE,
        RECORD_PAYMENT_INFO_TABLE
    );


    /* Talent Tables */
    String TALENT_TABLE = "talent";
    String TALENT_ADDITIONAL_INFO_TABLE = "talent_additional_info";
    String TALENT_NOTE_TABLE = "talent_note";
    String TALENT_TRACKING_NOTE_TABLE = "talent_tracking_note";
    String TALENT_CONTACT_TABLE = "talent_contact";
    String TALENT_CURRENT_LOCATION_TABLE = "talent_current_location";
    String TALENT_INDUSTRY_RELATION_TABLE = "talent_industry_relation";
    String TALENT_JOB_FUNCTION_RELATION_TABLE = "talent_job_function_relation";
    String TALENT_LANGUAGE_RELATION_TABLE = "talent_language_relation";
    String TALENT_OWNERSHIP_TABLE = "talent_ownership";
    String TALENT_RESUME_RELATION_TABLE = "talent_resume_relation";
    String TALENT_USER_RELATION_TABLE = "talent_user_relation";
    String TALENT_WORK_AUTHORIZATION_RELATION_TABLE = "talent_work_authorization_relation";
    List<String> talentTables = ImmutableList.of(
        TALENT_TABLE,
        TALENT_ADDITIONAL_INFO_TABLE,
        TALENT_NOTE_TABLE,
        TALENT_TRACKING_NOTE_TABLE,
        TALENT_CONTACT_TABLE,
        TALENT_CURRENT_LOCATION_TABLE,
        TALENT_INDUSTRY_RELATION_TABLE,
        TALENT_JOB_FUNCTION_RELATION_TABLE,
        TALENT_LANGUAGE_RELATION_TABLE,
        TALENT_OWNERSHIP_TABLE,
        TALENT_RESUME_RELATION_TABLE,
        TALENT_USER_RELATION_TABLE,
        TALENT_WORK_AUTHORIZATION_RELATION_TABLE
    );

    /* Application Tables */
    String TALENT_RECRUITMENT_PROCESS_TABLE = "talent_recruitment_process";
    String TALENT_RECRUITMENT_PROCESS_NOTE_TABLE = "talent_recruitment_process_note";
    String TALENT_RECRUITMENT_PROCESS_COMMISSION_TABLE = "talent_recruitment_process_commission";
    String TALENT_RECRUITMENT_PROCESS_ELIMINATE_TABLE = "talent_recruitment_process_eliminate";
    String TALENT_RECRUITMENT_PROCESS_INTERVIEW_TABLE = "talent_recruitment_process_interview";
    String TALENT_RECRUITMENT_PROCESS_IPG_AGREED_PAY_RATE_TABLE = "talent_recruitment_process_ipg_agreed_pay_rate";
    String TALENT_RECRUITMENT_PROCESS_IPG_CONTRACT_FEE_CHARGE_TABLE = "talent_recruitment_process_ipg_contract_fee_charge";
    String TALENT_RECRUITMENT_PROCESS_IPG_FTE_SALARY_PACKAGE_TABLE = "talent_recruitment_process_ipg_fte_salary_package";
    String TALENT_RECRUITMENT_PROCESS_IPG_OFFER_ACCEPT_TABLE = "talent_recruitment_process_ipg_offer_accept";
    String TALENT_RECRUITMENT_PROCESS_IPG_OFFER_LETTER_COST_RATE_TABLE = "talent_recruitment_process_ipg_offer_letter_cost_rate";
    String TALENT_RECRUITMENT_PROCESS_KPI_USER_TABLE = "talent_recruitment_process_kpi_user";
    String TALENT_RECRUITMENT_PROCESS_NODE_TABLE = "talent_recruitment_process_node";
    String TALENT_RECRUITMENT_PROCESS_OFFER_TABLE = "talent_recruitment_process_offer";
    String TALENT_RECRUITMENT_PROCESS_OFFER_FEE_CHARGE_TABLE = "talent_recruitment_process_offer_fee_charge";
    String TALENT_RECRUITMENT_PROCESS_OFFER_SALARY_TABLE = "talent_recruitment_process_offer_salary";
    String TALENT_RECRUITMENT_PROCESS_OFFER_SALARY_PACKAGE_TABLE = "talent_recruitment_process_offer_salary_package";
    String TALENT_RECRUITMENT_PROCESS_ONBOARD_TABLE = "talent_recruitment_process_onboard";
    String TALENT_RECRUITMENT_PROCESS_ONBOARD_CLIENT_INFO_TABLE = "talent_recruitment_process_onboard_client_info";
    String TALENT_RECRUITMENT_PROCESS_ONBOARD_DATE_TABLE = "talent_recruitment_process_onboard_date";
    String TALENT_RECRUITMENT_PROCESS_ONBOARD_WORK_LOCATION_TABLE = "talent_recruitment_process_onboard_work_location";
    String TALENT_RECRUITMENT_PROCESS_RESIGNATION_TABLE = "talent_recruitment_process_resignation";
    String TALENT_RECRUITMENT_PROCESS_STOP_STATISTICS_TABLE = "talent_recruitment_process_stop_statistics";
    String TALENT_RECRUITMENT_PROCESS_SUBMIT_TO_CLIENT_TABLE = "talent_recruitment_process_submit_to_client";
    String TALENT_RECRUITMENT_PROCESS_SUBMIT_TO_JOB_TABLE = "talent_recruitment_process_submit_to_job";
    String TALENT_RECRUITMENT_PROCESS_USER_RELATION = "talent_recruitment_process_user_relation";

    List<String> talentRecruitmentProcessTables = ImmutableList.of(
        TALENT_RECRUITMENT_PROCESS_TABLE,
        TALENT_RECRUITMENT_PROCESS_NOTE_TABLE,
        TALENT_RECRUITMENT_PROCESS_COMMISSION_TABLE,
        TALENT_RECRUITMENT_PROCESS_ELIMINATE_TABLE,
        TALENT_RECRUITMENT_PROCESS_INTERVIEW_TABLE,
        TALENT_RECRUITMENT_PROCESS_IPG_AGREED_PAY_RATE_TABLE,
        TALENT_RECRUITMENT_PROCESS_IPG_CONTRACT_FEE_CHARGE_TABLE,
        TALENT_RECRUITMENT_PROCESS_IPG_FTE_SALARY_PACKAGE_TABLE,
        TALENT_RECRUITMENT_PROCESS_IPG_OFFER_ACCEPT_TABLE,
        TALENT_RECRUITMENT_PROCESS_IPG_OFFER_LETTER_COST_RATE_TABLE,
        TALENT_RECRUITMENT_PROCESS_KPI_USER_TABLE,
        TALENT_RECRUITMENT_PROCESS_NODE_TABLE,
        TALENT_RECRUITMENT_PROCESS_OFFER_TABLE,
        TALENT_RECRUITMENT_PROCESS_OFFER_FEE_CHARGE_TABLE,
        TALENT_RECRUITMENT_PROCESS_OFFER_SALARY_TABLE,
        TALENT_RECRUITMENT_PROCESS_OFFER_SALARY_PACKAGE_TABLE,
        TALENT_RECRUITMENT_PROCESS_ONBOARD_TABLE,
        TALENT_RECRUITMENT_PROCESS_ONBOARD_CLIENT_INFO_TABLE,
        TALENT_RECRUITMENT_PROCESS_ONBOARD_DATE_TABLE,
        TALENT_RECRUITMENT_PROCESS_ONBOARD_WORK_LOCATION_TABLE,
        TALENT_RECRUITMENT_PROCESS_RESIGNATION_TABLE,
        TALENT_RECRUITMENT_PROCESS_STOP_STATISTICS_TABLE,
        TALENT_RECRUITMENT_PROCESS_SUBMIT_TO_CLIENT_TABLE,
        TALENT_RECRUITMENT_PROCESS_SUBMIT_TO_JOB_TABLE,
        TALENT_RECRUITMENT_PROCESS_USER_RELATION
    );

    /* Tenant Tables */
    String TENANT_TABLE = "tenant";
    String TENANT_ADDRESS_TABLE = "tenant_address";
    List<String> tenantTables = ImmutableList.of(
        TENANT_TABLE,
        TENANT_ADDRESS_TABLE
    );

    /* Timesheet Tables */
    String TIME_SHEET_EXPENSE_WEEK_ENDING_RECORD_TABLE = "time_sheet_expense_week_ending_record";
    String TIME_SHEET_HOLIDAY_RECORD_TABLE = "time_sheet_holiday_record";
    String TIME_SHEET_RECORD_TABLE = "time_sheet_record";
    String TIME_SHEET_USER_TABLE = "time_sheet_user";
    String TIME_SHEET_WEEK_ENDING_RECORD_TABLE = "time_sheet_week_ending_record";
    String TIMESHEET_APPROVE_RECORD_TABLE = "timesheet_approve_record";
    String TIMESHEET_BREAKTIME_RECORD_TABLE = "timesheet_breaktime_record";
    String TIMESHEET_CALCULATE_STATE_TABLE = "timesheet_calculate_state";
    String TIMESHEET_COMMENTS_TABLE = "timesheet_comments";
    String TIMESHEET_EXPENSE_RECORD_TABLE = "timesheet_expense_record";
    String TIMESHEET_GOOGLE_HOLIDAY_TABLE = "timesheet_google_holiday";
    String TIMESHEET_MANAGER_TABLE = "timesheet_manager";
    String TIMESHEET_TALENT_ASSIGNMENT_TABLE = "timesheet_talent_assignment";
    List<String> timesheetTables = ImmutableList.of(
        TIME_SHEET_EXPENSE_WEEK_ENDING_RECORD_TABLE,
        TIME_SHEET_HOLIDAY_RECORD_TABLE,
        TIME_SHEET_RECORD_TABLE,
        TIME_SHEET_USER_TABLE,
        TIME_SHEET_WEEK_ENDING_RECORD_TABLE,
        TIMESHEET_APPROVE_RECORD_TABLE,
        TIMESHEET_BREAKTIME_RECORD_TABLE,
        TIMESHEET_CALCULATE_STATE_TABLE,
        TIMESHEET_COMMENTS_TABLE,
        TIMESHEET_EXPENSE_RECORD_TABLE,
        TIMESHEET_GOOGLE_HOLIDAY_TABLE,
        TIMESHEET_MANAGER_TABLE,
        TIMESHEET_TALENT_ASSIGNMENT_TABLE
    );

    /* User Tables */
    String USER_TABLE = "user";
    String USER_ACCOUNT_TABLE = "user_account";
    String USER_JOB_RELATION_TABLE = "user_job_relation";
    String CREDIT_TRANSACTION = "credit_transaction";
    String USER_ROLE_TABLE = "user_role";
    List<String> userTables = ImmutableList.of(
        USER_TABLE,
        USER_ACCOUNT_TABLE,
        USER_JOB_RELATION_TABLE,
        USER_ROLE_TABLE,
        CREDIT_TRANSACTION
    );

    /* Enum Tables */
    List<String> enumTables = ImmutableList.of(
        "enum_business_progress",
        "enum_company_client_level",
        "enum_company_contact_category",
        "enum_company_contact_tag",
        "enum_company_sale_lead_source",
        "enum_company_service_type",
        "enum_company_tag",
        "enum_contact_type",
//        "enum_country",
        "enum_currency",
//        "enum_degree",
        "enum_follow_up_contact_type",
        "enum_gender",
//        "enum_industry",
        "enum_invoicing_service_tax",
        "enum_job_priority",
        "enum_language",
        "enum_motivation",
        "enum_receiving_account",
        "enum_timezone",
        "enum_work_authorization"

    );

    List<String> dateDimensionTables = ImmutableList.of(
        "date_dimension"
    );

    List<String> ODS_TABLES = Stream.of(
            assignmentDetailTables,
            companyTables,
            currencyTables,
            invoiceTables,
            jobTables,
            contractorInvoiceTables,
            permissionTables,
            recruitmentTables,
            resumeTables,
            roleTables,
            startTables,
            talentTables,
            talentRecruitmentProcessTables,
            tenantTables,
            timesheetTables,
            userTables,
            enumTables,
            dateDimensionTables
        )
        .flatMap(List::stream)
        .distinct()
        .collect(Collectors.toList());
}

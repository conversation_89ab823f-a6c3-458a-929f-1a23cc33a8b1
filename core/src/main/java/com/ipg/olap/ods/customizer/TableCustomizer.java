package com.ipg.olap.ods.customizer;

import com.google.common.collect.ImmutableList;
import com.ipg.olap.ods.table.customizer.*;
import com.ipg.olap.scmema.StarRocksTable;

import java.util.List;

public interface TableCustomizer {

    List<TableCustomizer> CUSTOMIZERS = ImmutableList.of(
        new AssignmentCustomizer(),
        new CompanyCustomizer(),
        new InvoiceCustomizer(),
        new JobCustomizer(),
        new StartCustomizer(),
        new ContractorInvoiceCustomizer(),
        new TalentCustomizer(),
        new ApplicationCustomizer(),
        new TimesheetCustomizer(),
        new UserCustomizer(),
        // common customizer must be the last one
        new CommonCustomizer()
    );

    static StarRocksTable doCustomize(StarRocksTable table) {
        return CUSTOMIZERS.stream().filter(customizer -> customizer.isCustomizable(table.getTableName()))
            .reduce(
                table,
                (currentTable, customizer) -> {
                    if (currentTable.beCustomized()) {
                        return currentTable;
                    } else {
                        return customizer.customize(currentTable);
                    }
                },
                (table1, table2) -> table2
            );
    }

    StarRocksTable customize(StarRocksTable table);

    boolean isCustomizable(String tableName);

}

package com.ipg.olap.ods.customizer;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.ipg.olap.scmema.StarRocksTable;

import java.util.List;

import static com.ipg.olap.ods.customizer.Constants.CREATED_DATE;
import static com.ipg.olap.ods.customizer.Constants.ID;

public class CustomizeHelper {

    public static StarRocksTable specifyKey(StarRocksTable table, String specifiedKey) {
        List<String> keys = ImmutableList.of(specifiedKey, ID);
        return table.toBuilder().setTableKeys(keys)
            .setOrderKeys(ImmutableList.of(specifiedKey))
            .setDistributionKeys(ImmutableList.of(specifiedKey))
//            .setTableProperties(ImmutableMap.of("colocate_with", "colocate_group_" + specifiedKey))
            .setColumns(ColumnSorter.sortColumns(table.getColumns(), keys))
            .build()
            .setCustomized(true);
    }

    public static StarRocksTable specifyKeyAndPartition(StarRocksTable table, String specifiedKey, String granularity) {
        List<String> keys = ImmutableList.of(specifiedKey, ID, CREATED_DATE);
        return table.toBuilder().setTableKeys(keys)
            .setOrderKeys(ImmutableList.of(specifiedKey))
            .setDistributionKeys(ImmutableList.of(specifiedKey))
//            .setTableProperties(ImmutableMap.of("colocate_with", "colocate_group_" + specifiedKey))
            .setPartitionExpress(String.format("date_trunc('%s', `%s`)", granularity, CREATED_DATE))
            .setColumns(ColumnSorter.sortColumns(table.getColumns(), keys))
            .build()
            .setCustomized(true);
    }

}

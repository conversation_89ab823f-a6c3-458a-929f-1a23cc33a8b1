package com.ipg.olap.ods.customizer;

import com.ipg.olap.scmema.StarRocksColumn;

import java.util.List;
import java.util.stream.Collectors;

public class ColumnSorter {

    public static List<StarRocksColumn> sortColumns(List<StarRocksColumn> columns, List<String> primaryKeys) {
        return columns.stream().sorted((c1, c2) -> {
            boolean isC1Primary = primaryKeys.contains(c1.getColumnName());
            boolean isC2Primary = primaryKeys.contains(c2.getColumnName());

            // If both are primary or non-primary, maintain their order
            if (isC1Primary && isC2Primary) {
                return primaryKeys.indexOf(c1.getColumnName()) - primaryKeys.indexOf(c2.getColumnName());
            } else if (isC1Primary) {
                // c1 is primary, c2 is not
                return -1;
            } else if (isC2Primary) {
                // c2 is primary, c1 is not
                return 1;
            }
            // Both are non-primary
            return 0;
        }).collect(Collectors.toList());
    }
}

package com.ipg.olap.scmema;

import com.google.common.collect.ImmutableMap;
import org.apache.flink.util.Preconditions;

import javax.annotation.Nullable;
import java.util.*;
import java.util.stream.Collectors;

public class StarRocksTable {
    private final String databaseName;
    private final String tableName;
    private final TableType tableType;
    private final List<StarRocksColumn> columns;
    @Nullable
    private final List<String> tableKeys;
    @Nullable
    private final List<String> orderKeys;
    @Nullable
    private final List<String> distributionKeys;
    private final String partitionExpress;
    @Nullable
    private final Integer numBuckets;
    @Nullable
    private final String comment;
    private final Map<String, String> properties;
    @Nullable
    private volatile Map<String, StarRocksColumn> columnMap;

    private boolean customized;

    private StarRocksTable(String databaseName, String tableName, TableType tableType, List<StarRocksColumn> columns, @Nullable List<String> tableKeys, @Nullable List<String> distributionKeys, @Nullable List<String> orderKeys, String partitionExpress, @Nullable Integer numBuckets, @Nullable String comment, Map<String, String> properties) {
        Preconditions.checkNotNull(databaseName);
        Preconditions.checkNotNull(tableName);
        Preconditions.checkNotNull(tableType);
        Preconditions.checkArgument(columns != null && !columns.isEmpty());
        this.databaseName = databaseName;
        this.tableName = tableName;
        this.tableType = tableType;
        this.columns = columns;
        this.tableKeys = tableKeys;
        this.orderKeys = orderKeys;
        this.distributionKeys = distributionKeys;
        this.partitionExpress = partitionExpress;
        this.numBuckets = numBuckets;
        this.comment = comment;
        this.properties = (Map) Preconditions.checkNotNull(properties);
    }

    public String getDatabaseName() {
        return this.databaseName;
    }

    public String getTableName() {
        return this.tableName;
    }

    public TableType getTableType() {
        return this.tableType;
    }

    public List<StarRocksColumn> getColumns() {
        return this.columns;
    }

    public Optional<List<String>> getTableKeys() {
        return Optional.ofNullable(this.tableKeys);
    }

    public Optional<List<String>> getOrderKeys() {
        return Optional.ofNullable(this.orderKeys);
    }

    public Optional<List<String>> getDistributionKeys() {
        return Optional.ofNullable(this.distributionKeys);
    }

    public Optional<String> getPartitionExpress() {
        return Optional.ofNullable(this.partitionExpress);
    }

    public Optional<Integer> getNumBuckets() {
        return Optional.ofNullable(this.numBuckets);
    }

    public Optional<String> getComment() {
        return Optional.ofNullable(this.comment);
    }

    public Map<String, String> getProperties() {
        return this.properties;
    }

    public StarRocksColumn getColumn(String columnName) {
        if (this.columnMap == null) {
            synchronized (this) {
                if (this.columnMap == null) {
                    this.columnMap = new HashMap();

                    for (StarRocksColumn column : this.columns) {
                        this.columnMap.put(column.getColumnName(), column);
                    }
                }
            }
        }

        return this.columnMap.get(columnName);
    }

    public boolean beCustomized() {
        return customized;
    }

    public StarRocksTable setCustomized(boolean customized) {
        this.customized = customized;
        return this;
    }



    public String toString() {
        return "StarRocksTable{databaseName='" + this.databaseName + '\'' + ", tableName='" + this.tableName + '\'' + ", tableType=" + this.tableType + ", columns=" + this.columns + ", tableKeys=" + this.tableKeys + ", orderKeys=" + this.orderKeys + ", distributionKeys=" + this.distributionKeys + ", numBuckets=" + this.numBuckets + ", comment='" + this.comment + '\'' + ", properties=" + this.properties + '}';
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        } else if (o != null && this.getClass() == o.getClass()) {
            StarRocksTable that = (StarRocksTable) o;
            return Objects.equals(this.databaseName, that.databaseName) && Objects.equals(this.tableName, that.tableName) && this.tableType == that.tableType && Objects.equals(this.columns, that.columns) && Objects.equals(this.tableKeys, that.tableKeys) && Objects.equals(this.orderKeys, that.orderKeys) && Objects.equals(this.distributionKeys, that.distributionKeys) && Objects.equals(this.numBuckets, that.numBuckets) && Objects.equals(this.comment, that.comment) && Objects.equals(this.properties, that.properties);
        } else {
            return false;
        }
    }

    public static enum TableType {
        UNKNOWN,
        DUPLICATE_KEY,
        AGGREGATE,
        UNIQUE_KEY,
        PRIMARY_KEY;

        private TableType() {
        }
    }

    public Builder toBuilder() {
       return new Builder().setDatabaseName(this.databaseName).setTableName(this.tableName).setTableType(this.tableType)
           .setColumns(this.columns).setTableKeys(this.tableKeys).setDistributionKeys(this.distributionKeys)
           .setNumBuckets(this.numBuckets).setComment(this.comment).setTableProperties(this.properties);
    }

    public static class Builder {
        private String databaseName;
        private String tableName;
        private TableType tableType;
        private List<StarRocksColumn> columns = new ArrayList();
        private List<String> tableKeys;
        private List<String> distributionKeys;
        private List<String> orderKeys;
        private String partitionExpress;
        private Integer numBuckets;
        private String comment;
        private Map<String, String> properties = new HashMap();

        public Builder() {
        }

        public Builder setDatabaseName(String databaseName) {
            this.databaseName = databaseName;
            return this;
        }

        public Builder setTableName(String tableName) {
            this.tableName = tableName;
            return this;
        }

        public Builder setTableType(TableType tableType) {
            this.tableType = tableType;
            return this;
        }

        public Builder setColumns(List<StarRocksColumn> columns) {
            this.columns = columns;
            return this;
        }

        public Builder setTableKeys(List<String> tableKeys) {
            this.tableKeys = tableKeys;
            return this;
        }

        public Builder setDistributionKeys(List<String> distributionKeys) {
            this.distributionKeys = distributionKeys;
            return this;
        }

        public Builder setOrderKeys(List<String> orderKeys) {
            this.orderKeys = orderKeys;
            return this;
        }

        public Builder setPartitionExpress(String partitionExpress) {
            this.partitionExpress = partitionExpress;
            return this;
        }

        public Builder setNumBuckets(Integer numBuckets) {
            this.numBuckets = numBuckets;
            return this;
        }

        public Builder setComment(String comment) {
            this.comment = comment;
            return this;
        }

        public Builder setTableProperties(Map<String, String> properties) {
            if (this.properties == null) {
                this.properties = properties;
            } else {
                Map<String, String> mergedProperties = new HashMap<>(this.properties);
                mergedProperties.putAll(properties);
                this.properties = ImmutableMap.copyOf(mergedProperties);
            }
            return this;
        }

        private void requireTableKeyNotNull() {
            this.columns  = this.columns.stream().map(c -> {
                if (tableKeys.contains(c.getColumnName())) {
                   return c.toBuilder().setNullable(false).build();
                } else {
                    return c;
                }
            }).collect(Collectors.toList());
        }

        public StarRocksTable build() {
            requireTableKeyNotNull();
            return new StarRocksTable(this.databaseName, this.tableName, this.tableType, this.columns, this.tableKeys, this.distributionKeys, this.orderKeys, this.partitionExpress, this.numBuckets, this.comment, this.properties);
        }
    }
}

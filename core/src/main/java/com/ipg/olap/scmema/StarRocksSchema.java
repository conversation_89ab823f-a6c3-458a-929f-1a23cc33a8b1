package com.ipg.olap.scmema;

import com.ipg.olap.Env;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

public class StarRocksSchema {

    private static final String starRocksJdbcUrl = Env.starRocksJdbcUrl();
    private static final String starRocksUsername = Env.starRocksUser();
    private static final String starRocksPassword = Env.starRocksPassword();

    private Connection getConnection() throws SQLException {
        return DriverManager.getConnection(starRocksJdbcUrl, starRocksUsername, starRocksPassword);
    }
}

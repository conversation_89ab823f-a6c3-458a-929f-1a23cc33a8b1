package com.ipg.olap.scmema;

import org.apache.flink.util.Preconditions;

import javax.annotation.Nullable;
import java.io.Serializable;
import java.util.Objects;
import java.util.Optional;

public class StarRocksColumn implements Serializable {
    private static final long serialVersionUID = 1L;
    private final String columnName;
    private final int ordinalPosition;
    private final String dataType;
    private final boolean isNullable;
    @Nullable
    private final String defaultValue;
    @Nullable
    private final Integer columnSize;
    @Nullable
    private final Integer decimalDigits;
    @Nullable
    private final String columnComment;

    private StarRocksColumn(String columnName, int ordinalPosition, String dataType, boolean isNullable, @Nullable String defaultValue, @Nullable Integer columnSize, @Nullable Integer decimalDigits, @Nullable String columnComment) {
        this.columnName = (String)Preconditions.checkNotNull(columnName);
        this.ordinalPosition = ordinalPosition;
        this.dataType = (String)Preconditions.checkNotNull(dataType);
        this.isNullable = isNullable;
        this.defaultValue = defaultValue;
        this.columnSize = columnSize;
        this.decimalDigits = decimalDigits;
        this.columnComment = columnComment;
    }

    public String getColumnName() {
        return this.columnName;
    }

    public int getOrdinalPosition() {
        return this.ordinalPosition;
    }

    public String getDataType() {
        return this.dataType;
    }

    public boolean isNullable() {
        return this.isNullable;
    }

    public Optional<String> getDefaultValue() {
        return Optional.ofNullable(this.defaultValue);
    }

    public Optional<Integer> getColumnSize() {
        return Optional.ofNullable(this.columnSize);
    }

    public Optional<Integer> getDecimalDigits() {
        return Optional.ofNullable(this.decimalDigits);
    }

    public Optional<String> getColumnComment() {
        return Optional.ofNullable(this.columnComment);
    }

    public String toString() {
        return "StarRocksColumn{columnName='" + this.columnName + '\'' + ", ordinalPosition=" + this.ordinalPosition + ", dataType='" + this.dataType + '\'' + ", isNullable=" + this.isNullable + ", defaultValue='" + this.defaultValue + '\'' + ", columnSize=" + this.columnSize + ", decimalDigits=" + this.decimalDigits + ", columnComment='" + this.columnComment + '\'' + '}';
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        } else if (o != null && this.getClass() == o.getClass()) {
            StarRocksColumn column = (StarRocksColumn)o;
            return this.ordinalPosition == column.ordinalPosition && this.isNullable == column.isNullable && Objects.equals(this.columnName, column.columnName) && this.dataType.equalsIgnoreCase(column.dataType) && Objects.equals(this.defaultValue, column.defaultValue) && Objects.equals(this.columnSize, column.columnSize) && Objects.equals(this.decimalDigits, column.decimalDigits) && Objects.equals(this.columnComment, column.columnComment);
        } else {
            return false;
        }
    }

    public Builder toBuilder() {
        return new Builder().setColumnName(this.columnName).setOrdinalPosition(this.ordinalPosition).setDataType(this.dataType).setNullable(this.isNullable).setDefaultValue(this.defaultValue).setColumnSize(this.columnSize).setDecimalDigits(this.decimalDigits).setColumnComment(this.columnComment);
    }

    public static class Builder {
        private String columnName;
        private int ordinalPosition;
        private String dataType;
        private boolean isNullable = true;
        private String defaultValue;
        private Integer columnSize;
        private Integer decimalDigits;
        private String columnComment;

        public Builder() {
        }

        public Builder setColumnName(String columnName) {
            this.columnName = columnName;
            return this;
        }

        public Builder setOrdinalPosition(int ordinalPosition) {
            this.ordinalPosition = ordinalPosition;
            return this;
        }

        public Builder setDataType(String dataType) {
            this.dataType = dataType;
            return this;
        }

        public Builder setNullable(boolean isNullable) {
            this.isNullable = isNullable;
            return this;
        }

        public Builder setDefaultValue(String defaultValue) {
            this.defaultValue = defaultValue;
            return this;
        }

        public Builder setColumnSize(Integer columnSize) {
            this.columnSize = columnSize;
            return this;
        }

        public Builder setDecimalDigits(Integer decimalDigits) {
            this.decimalDigits = decimalDigits;
            return this;
        }

        public Builder setColumnComment(String columnComment) {
            this.columnComment = columnComment;
            return this;
        }

        public StarRocksColumn build() {
            return new StarRocksColumn(this.columnName, this.ordinalPosition, this.dataType, this.isNullable, this.defaultValue, this.columnSize, this.decimalDigits, this.columnComment);
        }
    }
}

package com.ipg.olap.scmema;

import io.debezium.connector.mysql.antlr.MySqlAntlrDdlParser;
import io.debezium.jdbc.JdbcConnection;
import io.debezium.relational.Column;
import io.debezium.relational.Table;
import io.debezium.relational.Tables;
import io.debezium.text.ParsingException;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.cdc.common.schema.Schema;
import org.apache.flink.cdc.common.types.DataType;
import org.apache.flink.cdc.connectors.mysql.schema.MySqlFieldDefinition;
import org.apache.flink.cdc.connectors.mysql.schema.MySqlTableDefinition;
import org.apache.flink.cdc.connectors.mysql.utils.MySqlTypeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class MysqlSchemaGetter {

    private static final Logger LOG = LoggerFactory.getLogger(MysqlSchemaGetter.class);

    private MySqlAntlrDdlParser mySqlAntlrDdlParser;

    public Schema getSchema(JdbcConnection jdbc, io.debezium.relational.TableId tableId) {
        String ddlStatement = showCreateTable(jdbc, tableId);
        try {
            return parseDDL(ddlStatement, tableId);
        } catch (ParsingException pe) {
            LOG.warn(
                "Failed to parse DDL: \n{}\nWill try parsing by describing table.",
                ddlStatement,
                pe);
        }
        ddlStatement = describeTable(jdbc, tableId);
        return parseDDL(ddlStatement, tableId);
    }

    private String showCreateTable(JdbcConnection jdbc, io.debezium.relational.TableId tableId) {
        final String showCreateTableQuery =
            String.format("SHOW CREATE TABLE `%s`.`%s`", tableId.catalog(), tableId.table());
        try {
            return jdbc.queryAndMap(
                showCreateTableQuery,
                rs -> {
                    String ddlStatement = null;
                    while (rs.next()) {
                        ddlStatement = rs.getString(2);
                    }
                    return ddlStatement;
                });
        } catch (SQLException e) {
            throw new RuntimeException(
                String.format("Failed to show create table for %s", tableId), e);
        }
    }

    private String describeTable(JdbcConnection jdbc, io.debezium.relational.TableId tableId) {
        List<MySqlFieldDefinition> fieldMetas = new ArrayList<>();
        List<String> primaryKeys = new ArrayList<>();
        try {
            return jdbc.queryAndMap(
                String.format("DESC `%s`.`%s`", tableId.catalog(), tableId.table()),
                rs -> {
                    while (rs.next()) {
                        MySqlFieldDefinition meta = new MySqlFieldDefinition();
                        meta.setColumnName(rs.getString("Field"));
                        meta.setColumnType(rs.getString("Type"));
                        meta.setNullable(
                            StringUtils.equalsIgnoreCase(rs.getString("Null"), "YES"));
                        meta.setKey("PRI".equalsIgnoreCase(rs.getString("Key")));
                        meta.setUnique("UNI".equalsIgnoreCase(rs.getString("Key")));
                        meta.setDefaultValue(rs.getString("Default"));
                        meta.setExtra(rs.getString("Extra"));
                        if (meta.isKey()) {
                            primaryKeys.add(meta.getColumnName());
                        }
                        fieldMetas.add(meta);
                    }
                    return new MySqlTableDefinition(tableId, fieldMetas, primaryKeys).toDdl();
                });
        } catch (SQLException e) {
            throw new RuntimeException(String.format("Failed to describe table %s", tableId), e);
        }
    }

    private Schema parseDDL(String ddlStatement, io.debezium.relational.TableId tableId) {
        Table table = parseDdl(ddlStatement, tableId);

        List<Column> columns = table.columns();
        Schema.Builder tableBuilder = Schema.newBuilder();
        for (Column column : columns) {
            String colName = column.name();
            DataType dataType = MySqlTypeUtils.fromDbzColumn(column, true);
            if (!column.isOptional()) {
                dataType = dataType.notNull();
            }
            tableBuilder.physicalColumn(
                colName,
                dataType,
                column.comment(),
                column.defaultValueExpression().orElse(null));
        }

        List<String> primaryKey = table.primaryKeyColumnNames();
        if (Objects.nonNull(primaryKey) && !primaryKey.isEmpty()) {
            tableBuilder.primaryKey(primaryKey);
        }
        return tableBuilder.build();
    }

    private synchronized Table parseDdl(String ddlStatement, io.debezium.relational.TableId tableId) {
        MySqlAntlrDdlParser mySqlAntlrDdlParser = getParser();
        mySqlAntlrDdlParser.setCurrentDatabase(tableId.catalog());
        Tables tables = new Tables();
        mySqlAntlrDdlParser.parse(ddlStatement, tables);
        return tables.forTable(tableId);
    }

    private synchronized MySqlAntlrDdlParser getParser() {
        if (mySqlAntlrDdlParser == null) {
            mySqlAntlrDdlParser = new MySqlAntlrDdlParser();
        }
        return mySqlAntlrDdlParser;
    }
}

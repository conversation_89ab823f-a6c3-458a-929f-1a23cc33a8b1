# Flink + Paimon 流处理方案

## 概述

本项目实现了基于 **Flink + Paimon** 的流处理架构，用于替代 StarRocks 物化视图。

### 核心优势

✅ **状态分离**: ODS 数据存储在 Paimon，减少 Flink 内存压力
✅ **轻量级存储**: 使用共享 PVC，无需额外存储服务
✅ **真正流处理**: 支持增量计算，避免全量刷新
✅ **生产就绪**: 完整的 K8s 部署方案

## 架构设计

```
MySQL 业务数据
    ↓ (Flink CDC)
Paimon ODS (共享 PVC)
    ↓ (Flink SQL 实时计算)
StarRocks DWD/ADS
```

### 与传统方案对比

| 方案 | 内存需求 | 存储成本 | 实时性 | 维护复杂度 |
|------|----------|----------|--------|------------|
| 物化视图 | 低 | 高 | 差 (小时级) | 高 |
| 纯 Flink SQL | 极高 (50-100GB) | 低 | 好 (秒级) | 中 |
| **Flink + Paimon** | **低 (5-10GB)** | **低** | **好 (秒级)** | **低** |

## 快速开始

### 本地开发环境

1. **设置环境变量**
```bash
export MYSQL_HOST="your-mysql-host"
export MYSQL_USER="your-mysql-user"
export MYSQL_PASSWORD="your-mysql-password"
export STARROCKS_JDBC_URL="***********************************"
export STARROCKS_LOAD_URL="your-starrocks-fe:8030"
export STARROCKS_USER="root"
export STARROCKS_PASSWORD="your-starrocks-password"
```

2. **运行 Demo**
```bash
# 编译项目
mvn clean package -DskipTests -pl flink-stream-processing

# 运行团队层级 Demo
./flink-stream-processing/scripts/run-paimon-demo.sh run
```

### K8s 生产环境

1. **构建 Docker 镜像**
```bash
./flink-stream-processing/scripts/build-docker-image.sh build
```

2. **部署到 K8s**
```bash
# 设置环境变量
export MYSQL_PASSWORD="your-password"
export STARROCKS_PASSWORD="your-password"

# 一键部署
./flink-stream-processing/scripts/deploy-k8s-paimon.sh deploy
```

## 项目结构

```
flink-stream-processing/
├── src/main/java/com/ipg/olap/stream/
│   └── demo/
│       └── PaimonTeamHierarchyDemo.java    # 团队层级 Demo
├── k8s/
│   ├── paimon-storage.yaml                 # K8s 存储配置
│   └── paimon-job.yaml                     # K8s 作业配置
├── docker/
│   ├── Dockerfile                          # Docker 镜像定义
│   ├── docker-entrypoint.sh               # 容器启动脚本
│   └── flink-conf.yaml                    # Flink 配置
└── scripts/
    ├── run-paimon-demo.sh                  # 本地 Demo 脚本
    ├── deploy-k8s-paimon.sh               # K8s 部署脚本
    └── build-docker-image.sh              # Docker 构建脚本
```

## Demo 说明

### 团队层级处理 Demo

**功能**: 演示 MySQL CDC → Paimon ODS → StarRocks DWD 的完整数据流

**数据流程**:
1. 从 MySQL `permission_team` 表读取 CDC 数据
2. 存储到 Paimon ODS 表
3. 基于 Paimon 数据计算团队层级关系
4. 输出到 StarRocks `mv_team_hierarchy_demo` 表

**核心 SQL**:
```sql
-- 团队层级关系计算
SELECT
    child.id AS child_team_id,
    child.name AS child_team_name,
    child.code AS child_team_code,
    parent.id AS parent_team_id,
    parent.name AS parent_team_name,
    parent.code AS parent_team_code,
    parent.parent_id AS parent_team_parent_id,
    parent.level AS parent_team_level,
    parent.is_leaf AS parent_team_is_leaf,
    child.tenant_id
FROM paimon_catalog.ods_demo.permission_team child
INNER JOIN paimon_catalog.ods_demo.permission_team parent ON (
    child.code LIKE CONCAT(parent.code, '%')
    OR child.code = parent.code
)
WHERE child.deleted = 0 AND parent.deleted = 0
```

## 配置说明

### Paimon 配置

```yaml
# 存储配置
warehouse: "/opt/paimon/warehouse"
file.format: "parquet"
bucket: "4"

# 性能优化
compaction.max.file-num: "50"
snapshot.time-retained: "1h"
write.buffer.size: "128MB"
```

### Flink 配置

```yaml
# 内存配置
jobmanager.memory.process.size: 2g
taskmanager.memory.process.size: 4g
taskmanager.numberOfTaskSlots: 2

# Checkpoint 配置
execution.checkpointing.interval: 30s
execution.checkpointing.mode: EXACTLY_ONCE
state.backend: rocksdb
state.backend.incremental: true
```

## 监控和调试

### 本地环境

- **Flink Web UI**: http://localhost:8081
- **日志查看**: `$FLINK_HOME/log/`
- **Paimon 数据**: `/tmp/paimon-demo-warehouse/`

### K8s 环境

```bash
# 查看 Flink Web UI
kubectl port-forward svc/paimon-flink-ui 8081:8081 -n olap

# 查看作业日志
kubectl logs job/paimon-team-hierarchy-job -n olap -f

# 查看 Paimon 数据
kubectl exec -it paimon-debug -n olap -- ls -la /opt/paimon/warehouse/
```

## 扩展指南

### 添加新的数据处理

1. **创建新的 Demo 类**
```java
public class NewDataProcessingDemo {
    // 参考 PaimonTeamHierarchyDemo 实现
}
```

2. **更新 K8s 配置**
```yaml
# 在 paimon-job.yaml 中添加新的 Job
```

3. **部署新的处理逻辑**
```bash
./scripts/deploy-k8s-paimon.sh job
```

### 性能调优

1. **调整并行度**: 修改 `parallelism.default`
2. **优化内存**: 调整 `taskmanager.memory.process.size`
3. **调整 Checkpoint**: 修改 `execution.checkpointing.interval`

## 故障排除

### 常见问题

1. **PVC 无法绑定**: 检查存储类是否支持 ReadWriteMany
2. **Flink 作业失败**: 查看作业日志，检查数据库连接
3. **数据不同步**: 检查 MySQL binlog 配置和权限

### 调试命令

```bash
# 检查 PVC 状态
kubectl get pvc -n olap

# 查看 Pod 状态
kubectl get pods -n olap

# 查看作业状态
kubectl get jobs -n olap

# 进入调试容器
kubectl exec -it paimon-debug -n olap -- bash
```

## 下一步计划

1. **扩展更多物化视图**: 基于当前 Demo 扩展其他业务表
2. **性能优化**: 根据实际数据量调优配置
3. **监控完善**: 添加 Prometheus 监控指标
4. **自动化测试**: 添加集成测试用例

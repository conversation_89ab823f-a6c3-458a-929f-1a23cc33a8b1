# 如何添加新的表处理器

本文档说明如何在重构后的架构中添加新的表处理逻辑。

## 架构概述

重构后的系统采用了模块化的处理器模式：

```
ApplicationMaterializeViewDwdJob (主任务编排器)
├── TableProcessorFactory (处理器工厂)
├── PaimonConfig (配置管理)
├── TableProcessor (处理器接口)
├── AbstractTableProcessor (抽象基类)
└── impl/
    ├── ApplicationFactProcessor
    ├── ApplicationDimensionProcessor
    └── YourNewProcessor (新增处理器在这里)
```

## 添加新表处理器的步骤

### 1. 创建新的处理器类

在 `src/main/java/com/ipg/olap/stream/processor/impl/` 目录下创建新的处理器类：

```java
package com.ipg.olap.stream.processor.impl;

import com.ipg.olap.stream.processor.AbstractTableProcessor;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

/**
 * 您的新表处理器
 * 
 * 负责处理 dwd_your_table 表的创建和数据写入
 * 实现特定的业务逻辑
 */
public class YourNewTableProcessor extends AbstractTableProcessor {
    
    public YourNewTableProcessor() {
        super("YourNewTableProcessor", "dwd_apn.dwd_your_table");
    }
    
    @Override
    public String[] getRequiredSourceTables() {
        return new String[]{
            "source_table_1",
            "source_table_2",
            // 添加您需要的源表
        };
    }
    
    @Override
    public void createTargetTable(StreamTableEnvironment tableEnv) {
        String createTableDDL = """
            CREATE TABLE IF NOT EXISTS dwd_apn.dwd_your_table (
                id BIGINT,
                name STRING,
                created_time TIMESTAMP,
                -- 定义您的表结构
                PRIMARY KEY (id) NOT ENFORCED
            ) WITH (
                %s
            )""".formatted(getStandardPaimonTableConfig());
        
        tableEnv.executeSql(createTableDDL);
        System.out.println("新表创建成功: " + getTargetTableName());
    }
    
    @Override
    public void executeProcessing(StreamTableEnvironment tableEnv) {
        String processingSQL = """
            INSERT INTO dwd_apn.dwd_your_table
            SELECT 
                id,
                name,
                created_time
            FROM paimon_catalog.ods_apn.source_table_1
            WHERE condition = 'your_condition'
            """;
        
        System.out.println("执行新表处理 SQL:");
        System.out.println(processingSQL);
        
        tableEnv.executeSql(processingSQL);
        System.out.println("新表流式处理已提交");
    }
}
```

### 2. 在工厂中注册新处理器

在 `TableProcessorFactory.java` 的静态初始化块中添加：

```java
static {
    // 注册现有的处理器
    registerProcessor(new ApplicationFactProcessor());
    registerProcessor(new ApplicationDimensionProcessor());
    
    // 添加您的新处理器
    registerProcessor(new YourNewTableProcessor());
}
```

### 3. 运行测试

运行 `ApplicationMaterializeViewDwdJob`，您的新处理器将自动被执行。

## 处理器的执行流程

每个处理器都会按以下顺序执行：

1. **validateSourceTables()** - 验证所需的源表是否存在
2. **createTargetTable()** - 创建目标表（如果不存在）
3. **executeProcessing()** - 执行数据处理逻辑

## 配置说明

### Paimon 表配置

所有处理器都使用标准的 Paimon 配置：

- `changelog-producer = 'lookup'` - 使用 lookup 模式
- `bucket = '8'` - 8 个分桶
- 自动压缩、缓冲刷新等优化配置

### 环境配置

通过 `Env` 类从环境变量读取配置：

- `PARALLELISM` - 并行度
- `CHECKPOINT_INTERVAL` - 检查点间隔
- MySQL 和 Paimon 相关配置

## 最佳实践

1. **命名规范**：
   - 处理器类名以 `Processor` 结尾
   - 目标表使用 `dwd_apn.` 前缀

2. **错误处理**：
   - 在 `getRequiredSourceTables()` 中列出所有依赖的源表
   - 让基类处理源表验证逻辑

3. **SQL 优化**：
   - 使用 Java 17 的文本块让 SQL 更易读
   - 添加适当的注释说明业务逻辑

4. **日志输出**：
   - 在关键步骤添加日志输出
   - 使用一致的日志格式

## 示例：添加一个简单的聚合表处理器

```java
public class UserActivitySummaryProcessor extends AbstractTableProcessor {
    
    public UserActivitySummaryProcessor() {
        super("UserActivitySummaryProcessor", "dwd_apn.dwd_user_activity_summary");
    }
    
    @Override
    public String[] getRequiredSourceTables() {
        return new String[]{"user_activity_log"};
    }
    
    @Override
    public void createTargetTable(StreamTableEnvironment tableEnv) {
        String createTableDDL = """
            CREATE TABLE IF NOT EXISTS dwd_apn.dwd_user_activity_summary (
                user_id BIGINT,
                activity_date DATE,
                activity_count BIGINT,
                PRIMARY KEY (user_id, activity_date) NOT ENFORCED
            ) PARTITIONED BY (activity_date) WITH (
                %s
            )""".formatted(getStandardPaimonTableConfig());
        
        tableEnv.executeSql(createTableDDL);
        System.out.println("用户活动汇总表创建成功: " + getTargetTableName());
    }
    
    @Override
    public void executeProcessing(StreamTableEnvironment tableEnv) {
        String processingSQL = """
            INSERT INTO dwd_apn.dwd_user_activity_summary
            SELECT 
                user_id,
                DATE_FORMAT(activity_time, 'yyyy-MM-dd') AS activity_date,
                COUNT(*) AS activity_count
            FROM paimon_catalog.ods_apn.user_activity_log
            GROUP BY 
                user_id,
                DATE_FORMAT(activity_time, 'yyyy-MM-dd')
            """;
        
        tableEnv.executeSql(processingSQL);
        System.out.println("用户活动汇总处理已提交");
    }
}
```

通过这种模式，您可以快速添加新的表处理逻辑，而不需要修改主任务类或其他现有代码。

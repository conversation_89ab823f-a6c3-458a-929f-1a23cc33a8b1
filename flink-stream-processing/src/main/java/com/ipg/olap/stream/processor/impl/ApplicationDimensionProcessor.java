package com.ipg.olap.stream.processor.impl;

import com.ipg.olap.stream.processor.AbstractTableProcessor;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

public class ApplicationDimensionProcessor extends AbstractTableProcessor {

    public ApplicationDimensionProcessor() {
        super("ApplicationDimensionProcessor", "dwd_apn.dwd_application_dimension");
    }

    @Override
    public String[] getRequiredSourceTables() {
        return new String[]{
            "talent_recruitment_process",
            "recruitment_process",
            "job",
            "company",
            "job_talent_recommend_feedback",
            "credit_transaction"
        };
    }

    @Override
    public void createTargetTable(StreamTableEnvironment tableEnv) {
        String createTableDDL = """
            CREATE TABLE IF NOT EXISTS %s (
                tenant_id BIGINT,
                company_id BIGINT,
                company_name STRING,
                job_id BIGINT,
                job_title STRING,
                job_pteam_id BIGINT,
                talent_recruitment_process_id BIGINT,
                talent_id BIGINT,
                ai_score DOUBLE,
                recommend_feedback_id BIGINT,
                dt STRING,
                PRIMARY KEY (talent_recruitment_process_id, dt) NOT ENFORCED
            )  PARTITIONED BY (dt)
            WITH (
                %s
            )""".formatted(targetTableName, getStandardPaimonTableConfig());

        tableEnv.executeSql(createTableDDL);
        System.out.println("dwd table created: " + getTargetTableName());
    }

    @Override
    public void executeProcessing(StreamTableEnvironment tableEnv, StatementSet statementSet) {
        String dimensionProcessingSQL = """
            INSERT INTO %s
            SELECT
                trp.tenant_id,
                c.id AS company_id,
                c.full_business_name AS company_name,
                j.id AS job_id,
                j.title AS job_title,
                j.pteam_id AS job_pteam_id,
                trp.id AS talent_recruitment_process_id,
                trp.talent_id AS talent_id,
                trp.ai_score AS ai_score,
                rf.id AS recommend_feedback_id,
                DATE_FORMAT(trp.created_date, 'yyyy-MM') AS dt
            FROM (
                SELECT *,
                       PROCTIME() AS proc_time
                FROM paimon_catalog.ods_apn.talent_recruitment_process
            ) AS trp
            INNER JOIN paimon_catalog.ods_apn.recruitment_process AS rp
                ON trp.recruitment_process_id = rp.id
            LEFT JOIN paimon_catalog.ods_apn.job FOR SYSTEM_TIME AS OF trp.proc_time AS j
                ON trp.job_id = j.id
            LEFT JOIN paimon_catalog.ods_apn.company FOR SYSTEM_TIME AS OF trp.proc_time AS c
                ON j.company_id = c.id
            LEFT OUTER JOIN (
                SELECT DISTINCT
                    CASE
                        WHEN (rf.reason = 'UNLOCK_CANDIDATE' OR rf.reason = 'ADD_TO_TALENT')
                            THEN CAST(ct.talent_id AS BIGINT)
                        ELSE CAST(rf.talent_id AS BIGINT)
                    END AS talent_id,
                    rf.id,
                    rf.job_id
                FROM paimon_catalog.ods_apn.job_talent_recommend_feedback AS rf
                LEFT OUTER JOIN paimon_catalog.ods_apn.credit_transaction AS ct
                    ON ct.profile_id = rf.talent_id
                WHERE rf.reason IN ('ADD_TO_POSITION', 'ADD_TO_ASSOCIATION_JOB_FOLDER', 'UNLOCK_CANDIDATE', 'ADD_TO_TALENT')
            ) rf ON (rf.job_id = trp.job_id) AND (rf.talent_id = trp.talent_id)""".formatted(targetTableName);

        System.out.println(dimensionProcessingSQL);

        statementSet.addInsertSql(dimensionProcessingSQL);
    }
}

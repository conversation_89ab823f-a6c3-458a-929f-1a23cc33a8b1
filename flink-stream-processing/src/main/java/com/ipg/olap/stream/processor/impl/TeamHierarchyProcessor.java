package com.ipg.olap.stream.processor.impl;

import com.ipg.olap.stream.processor.AbstractTableProcessor;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 处理团队层级
 */
public class TeamHierarchyProcessor extends AbstractTableProcessor {

    private static final Logger log = LoggerFactory.getLogger(TeamHierarchyProcessor.class);

    public TeamHierarchyProcessor() {
        super("TeamHierarchyProcessor", "dwd_apn.dwd_team_hierarchy");
    }

    @Override
    public void createTargetTable(StreamTableEnvironment tableEnv) {
        String sql = """
            CREATE TABLE IF NOT EXISTS %s (
                child_team_id BIGINT,
                child_team_name STRING,
                child_team_code STRING,
                parent_team_id BIGINT,
                parent_team_name STRING,
                parent_team_code STRING,
                parent_team_parent_id BIGINT,
                parent_team_level INT,
                parent_team_is_leaf BOOLEAN,
                tenant_id BIGINT,
                PRIMARY KEY (child_team_id, parent_team_id) NOT ENFORCED
            )
            WITH (
                %s
            )
            """.formatted(targetTableName, getStandardPaimonTableConfig());
        tableEnv.executeSql(sql);
        log.info("Created target table {}", targetTableName);
    }

    @Override
    public void executeProcessing(StreamTableEnvironment tableEnv, StatementSet statementSet) {
        String sql = "INSERT INTO " + targetTableName + """ 
            
            SELECT
                child.id AS child_team_id,
                CAST(child.name AS STRING) AS child_team_name,
                child.code AS child_team_code,
                parent.id AS parent_team_id,
                parent.name AS parent_team_name,
                parent.code AS parent_team_code,
                parent.parent_id AS parent_team_parent_id,
                parent.level AS parent_team_level,
                CAST(parent.is_leaf AS BOOLEAN) AS parent_team_is_leaf,
                child.tenant_id
            FROM paimon_catalog.ods_apn.permission_team child
            INNER JOIN paimon_catalog.ods_apn.permission_team parent ON (
                child.code LIKE CONCAT(parent.code, '%')
                OR child.code = parent.code
            )
            WHERE child.deleted = 0 AND child.team_category_id IN (15, 20)
            AND parent.deleted = 0 AND parent.team_category_id IN (15, 20)
            """;
        statementSet.addInsertSql(sql);
        log.info("Executed processing for {}", targetTableName);
    }

    @Override
    public String[] getRequiredSourceTables() {
        return new String[]{"ods_apn.permission_team"};
    }
}

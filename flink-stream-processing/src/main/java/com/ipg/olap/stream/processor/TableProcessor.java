package com.ipg.olap.stream.processor;

import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

/**
 * 表处理器接口
 * 
 * 定义了处理单个表的标准流程，所有的表处理器都应该实现这个接口
 */
public interface TableProcessor {
    
    /**
     * 获取处理器名称
     * @return 处理器名称
     */
    String getProcessorName();
    
    /**
     * 获取目标表名称
     * @return 目标表名称
     */
    String getTargetTableName();
    
    /**
     * 验证所需的源表是否存在
     * @param tableEnv Flink Table Environment
     * @throws RuntimeException 如果源表不存在或无法访问
     */
    void validateSourceTables(StreamTableEnvironment tableEnv);
    
    /**
     * 创建目标表
     * @param tableEnv Flink Table Environment
     */
    void createTargetTable(StreamTableEnvironment tableEnv);
    
    /**
     * 执行数据处理逻辑
     *
     * @param tableEnv     Flink Table Environment
     * @param statementSet
     */
    void executeProcessing(StreamTableEnvironment tableEnv, StatementSet statementSet);
    
    /**
     * 获取所需的源表列表
     * @return 源表名称数组
     */
    String[] getRequiredSourceTables();
}

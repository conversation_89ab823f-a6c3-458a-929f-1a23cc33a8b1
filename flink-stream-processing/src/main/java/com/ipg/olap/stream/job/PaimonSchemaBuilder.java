package com.ipg.olap.stream.job;

import org.apache.paimon.catalog.Catalog;
import org.apache.paimon.catalog.FileSystemCatalog;
import org.apache.paimon.catalog.Identifier;
import org.apache.paimon.fs.Path;
import org.apache.paimon.fs.local.LocalFileIO;
import org.apache.paimon.schema.Schema;
import org.apache.paimon.schema.Schema.Builder;
import org.apache.paimon.types.DataField;
import org.apache.paimon.types.DataType;
import org.apache.paimon.types.DataTypes;

import java.sql.*;
import java.util.*;
import java.util.stream.Collectors;

public class PaimonSchemaBuilder {

    // ================= 配置 ===================
    private static final String MYSQL_URL = "***********************************";
    private static final String MYSQL_USER = "root";
    private static final String MYSQL_PASS = "password";
    private static final String DATABASE_NAME = "your_db";

    private static final String WAREHOUSE_PATH = "file:///tmp/paimon/warehouse"; // 修改为你的真实路径
    private static final String PARTITION_FIELD = "create_time"; // 分区字段
    // =========================================

    public static void main(String[] args) throws Exception {
        // 1. 初始化 Paimon Catalog
        Catalog catalog = new FileSystemCatalog(
            LocalFileIO.create(), new Path(WAREHOUSE_PATH)
        );

        // 确保数据库存在（Paimon catalog 会自动创建目录）
        if (catalog.getDatabase(DATABASE_NAME) == null) {
            catalog.createDatabase(DATABASE_NAME, true);
        }

        // 2. 连接 MySQL
        try (Connection conn = DriverManager.getConnection(MYSQL_URL, MYSQL_USER, MYSQL_PASS)) {
            List<String> tables = getTables(conn);
            for (String tableName : tables) {
                try {
                    Schema schema = buildPaimonSchema(conn, tableName);
                    if (schema != null) {
                        Identifier identifier = Identifier.create(DATABASE_NAME, tableName);
                        if (catalog.getTable(identifier) != null) {
                            System.out.println("Table already exists: " + identifier.getFullName());
                        } else {
                            catalog.createTable(identifier, schema, false);
                            System.out.println("✅ Created table: " + identifier.getFullName());
                        }
                    }
                } catch (Exception e) {
                    System.err.println("❌ Failed to create table " + tableName + ": " + e.getMessage());
                    e.printStackTrace();
                }
            }
        }

        System.out.println("🎉 All tables processed.");
    }

    private static List<String> getTables(Connection conn) throws SQLException {
        DatabaseMetaData metaData = conn.getMetaData();
        ResultSet rs = metaData.getTables(null, null, "%", new String[]{"TABLE"});
        List<String> tables = new ArrayList<>();
        while (rs.next()) {
            tables.add(rs.getString("TABLE_NAME"));
        }
        return tables;
    }

    private static Schema buildPaimonSchema(Connection conn, String tableName) throws SQLException {
        DatabaseMetaData metaData = conn.getMetaData();
        ResultSet columnsRs = metaData.getColumns(null, null, tableName, null);
        ResultSet primaryKeysRs = metaData.getPrimaryKeys(null, null, tableName);

        List<DataField> fields = new ArrayList<>();
        Set<String> primaryKeySet = new HashSet<>();
        List<String> partitionFieldSet = new ArrayList<>();
        boolean hasPartitionField = false;

        // 收集主键
        while (primaryKeysRs.next()) {
            primaryKeySet.add(primaryKeysRs.getString("COLUMN_NAME"));
        }

        // 遍历字段
        while (columnsRs.next()) {
            String colName = columnsRs.getString("COLUMN_NAME");
            String sqlType = columnsRs.getString("TYPE_NAME").toUpperCase();
            int decimalDigits = columnsRs.getInt("DECIMAL_DIGITS");

            DataType dataType = mapToPaimonType(sqlType, decimalDigits);
            if (dataType == null) {
                System.err.println("Unsupported type: " + sqlType + " for column " + colName);
                return null;
            }

            fields.add(new DataField(fields.size(), colName, dataType));

            // 检查是否为分区字段
            if (colName.equalsIgnoreCase(PARTITION_FIELD)) {
                hasPartitionField = true;
            }
        }

        if (fields.isEmpty()) {
            System.err.println("No columns found for table: " + tableName);
            return null;
        }

        // 构建 Schema
        Builder schemaBuilder = Schema.newBuilder();

        fields.forEach(field -> schemaBuilder.column(field.name(), Objects.requireNonNull(mapToPaimonType(field.type().toString(), 18))));

        // 添加主键
        if (!primaryKeySet.isEmpty()) {
            List<String> primaryKeyList = primaryKeySet.stream()
                .map(col -> fields.stream()
                    .filter(f -> f.name().equalsIgnoreCase(col))
                    .findFirst()
                    .map(DataField::name)
                    .orElse(null))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

            if (!primaryKeyList.isEmpty()) {
                schemaBuilder.primaryKey(primaryKeyList);
            }
        }

        // 添加分区
        if (hasPartitionField) {
            partitionFieldSet.add(PARTITION_FIELD);
            schemaBuilder.partitionKeys(partitionFieldSet);
        }

        return schemaBuilder.build();
    }

    private static DataType mapToPaimonType(String mysqlType, int decimalDigits) {
        return switch (mysqlType) {
            case "BIGINT" -> DataTypes.BIGINT();
            case "INT", "INTEGER" -> DataTypes.INT();
            case "SMALLINT" -> DataTypes.SMALLINT();
            case "TINYINT" -> DataTypes.TINYINT();
            case "DOUBLE" -> DataTypes.DOUBLE();
            case "FLOAT" -> DataTypes.FLOAT();
            case "DECIMAL" -> DataTypes.DECIMAL(18, decimalDigits);
            case "VARCHAR", "CHAR", "TEXT", "LONGTEXT" -> DataTypes.STRING();
            case "BOOLEAN", "BIT" -> DataTypes.BOOLEAN();
            case "DATE" -> DataTypes.DATE();
            case "DATETIME", "TIMESTAMP" -> DataTypes.TIMESTAMP(3);
            default -> {
                System.err.println("Unsupported MySQL type: " + mysqlType);
                yield null;
            }
        };
    }
}

package com.ipg.olap.stream.processor.impl;

import com.ipg.olap.stream.processor.AbstractTableProcessor;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

public class CreatedKpiProcessor extends AbstractTableProcessor {

    protected CreatedKpiProcessor() {
        super("CreatedKpiProcessor", "dwd_apn.dwd_created_kpi");
    }

    @Override
    public void createTargetTable(StreamTableEnvironment tableEnv) {

    }

    @Override
    public void executeProcessing(StreamTableEnvironment tableEnv, StatementSet statementSet) {

    }

    @Override
    public String[] getRequiredSourceTables() {
        return new String[0];
    }
}

package com.ipg.olap.stream.factory;

import com.ipg.olap.stream.processor.TableProcessor;
import com.ipg.olap.stream.processor.impl.ApplicationDimensionProcessor;
import com.ipg.olap.stream.processor.impl.ApplicationFactProcessor;
import com.ipg.olap.stream.processor.impl.TeamHierarchyProcessor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 表处理器工厂
 * <p>
 * 负责创建和管理所有的表处理器实例
 * 当需要添加新的表处理逻辑时，只需要在这里注册即可
 */
public class TableProcessorFactory {

    private static final Logger log = LoggerFactory.getLogger(TableProcessorFactory.class);

    private static final Map<String, TableProcessor> processors = new HashMap<>();
    private static boolean initialized = false;

    static {
        // 初始化所有处理器
        initializeProcessors();
    }

    /**
     * 初始化所有处理器
     * 在这里注册所有的表处理器
     */
    private static void initializeProcessors() {
        if (initialized) {
            return;
        }

        log.info("start to initialize table processors");

        // 注册所有处理器
        registerProcessor(new ApplicationFactProcessor());
        registerProcessor(new ApplicationDimensionProcessor());
        registerProcessor(new TeamHierarchyProcessor());
        registerProcessor(new TeamHierarchyProcessor());

        initialized = true;
        log.info("table processors initialized");
    }

    /**
     * 注册处理器
     *
     * @param processor 表处理器实例
     */
    public static void registerProcessor(TableProcessor processor) {
        processors.put(processor.getProcessorName(), processor);
        log.info("register processor: {} -> {}", processor.getProcessorName(), processor.getTargetTableName());
    }

    /**
     * 根据名称获取处理器
     *
     * @param processorName 处理器名称
     * @return 表处理器实例
     */
    public static TableProcessor getProcessor(String processorName) {
        TableProcessor processor = processors.get(processorName);
        if (processor == null) {
            throw new IllegalArgumentException("processor not found: " + processorName);
        }
        return processor;
    }

    /**
     * 获取所有注册的处理器
     *
     * @return 所有表处理器实例的列表
     */
    public static List<TableProcessor> getAllProcessors() {
        return processors.values().stream().toList();
    }

    /**
     * 获取所有已注册的处理器名称
     *
     * @return 处理器名称集合
     */
    public static Set<String> getProcessorNames() {
        return processors.keySet();
    }

    /**
     * 检查处理器是否已注册
     *
     * @param processorName 处理器名称
     * @return 是否已注册
     */
    public static boolean hasProcessor(String processorName) {
        return processors.containsKey(processorName);
    }

    /**
     * 获取处理器数量
     *
     * @return 已注册的处理器数量
     */
    public static int getProcessorCount() {
        return processors.size();
    }

    /**
     * 打印所有已注册的处理器信息
     */
    public static void printAllProcessors() {
        log.info("registered processors:");
        processors.forEach((name, processor) ->
            log.info("- {} -> {}", name, processor.getTargetTableName())
        );
        log.info("total: {} processors", processors.size());
    }
}

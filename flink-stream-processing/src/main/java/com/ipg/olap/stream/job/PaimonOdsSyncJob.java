package com.ipg.olap.stream.job;

import com.ipg.olap.stream.config.EnvConfiguration;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.paimon.flink.action.MultiTablesSinkMode;
import org.apache.paimon.flink.action.cdc.TypeMapping;
import org.apache.paimon.flink.action.cdc.mysql.MySqlSyncDatabaseAction;

import java.util.Map;

public class PaimonOdsSyncJob {

    public static void main(String[] args) throws Exception {
        // 1. 创建 Flink 执行环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        EnvConfiguration.initConfig(args[0]);

        Map<String, String> cdcActionConfig = EnvConfiguration.getCdcActionConfig();
        // 设置并行度
        env.setParallelism(Integer.parseInt(cdcActionConfig.getOrDefault("parallelism", "1")));
        env.enableCheckpointing(Integer.parseInt(cdcActionConfig.getOrDefault("checkpoint.interval", "10000")));

        // 2. 配置 Paimon Catalog
        Map<String, String> catalogConfig = EnvConfiguration.getPaimonConfig();

        // 3. 配置 MySQL CDC 源
        Map<String, String> mysqlConfig = EnvConfiguration.getMysqlConfig();
        // 4. 创建 MySqlSyncDatabaseAction
        String targetDatabase = cdcActionConfig.get("target-database");
        MySqlSyncDatabaseAction action = new MySqlSyncDatabaseAction(
            targetDatabase,
            catalogConfig,
            mysqlConfig
        );

        // 5. 配置同步选项
        action.ignoreIncompatible(true);
        action.includingTables(cdcActionConfig.getOrDefault("include-tables", ".*"));
        action.excludingTables(cdcActionConfig.getOrDefault("exclude-tables", null));
        action.withMode(MultiTablesSinkMode.COMBINED);
        action.withPrimaryKeys(cdcActionConfig.getOrDefault("primary-keys", "id"));
        action.withTypeMapping(TypeMapping.parse(new String[]{TypeMapping.TypeMappingMode.TINYINT1_NOT_BOOL.configString(), TypeMapping.TypeMappingMode.BIGINT_UNSIGNED_TO_BIGINT.configString()}));

        // 6.Paimon 表配置，专注于数据同步
        Map<String, String> tableConfig = EnvConfiguration.getPaimonTableConfig();
        action.withTableConfig(tableConfig);

        action.withStreamExecutionEnvironment(env);

        action.run();

    }
}

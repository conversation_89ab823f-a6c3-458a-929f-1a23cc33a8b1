package com.ipg.olap.stream.processor.impl;

import com.ipg.olap.stream.processor.AbstractTableProcessor;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

public class ApplicationFactProcessor extends AbstractTableProcessor {

    public ApplicationFactProcessor() {
        super("ApplicationFactProcessor", "dwd_apn.dwd_application_fact");
    }

    @Override
    public String[] getRequiredSourceTables() {
        return new String[]{
            "talent_recruitment_process_submit_to_job",
            "talent_recruitment_process_node",
            "talent_recruitment_process_submit_to_client",
            "talent_recruitment_process_interview",
            "talent_recruitment_process_offer",
            "talent_recruitment_process_ipg_offer_accept",
            "talent_recruitment_process_onboard",
            "talent_recruitment_process_onboard_date",
            "talent_recruitment_process_eliminate"
        };
    }

    @Override
    public void createTargetTable(StreamTableEnvironment tableEnv) {
        String createTableDDL = """
            CREATE TABLE IF NOT EXISTS dwd_apn.dwd_application_fact (
                talent_recruitment_process_id BIGINT,
                node_type INT,
                node_status INT,
                node_id BIGINT,
                progress INT,
                final_round BOOLEAN,
                add_date TIMESTAMP,
                event_date TIMESTAMP,
                dt STRING,
                operator BIGINT,
                PRIMARY KEY (talent_recruitment_process_id, node_type, node_id, dt) NOT ENFORCED
            ) PARTITIONED BY (dt)
            WITH (
                %s
            )""".formatted(getStandardPaimonTableConfig());

        tableEnv.executeSql(createTableDDL);
        System.out.println("dwd table created: " + getTargetTableName());
    }

    @Override
    public void executeProcessing(StreamTableEnvironment tableEnv, StatementSet statementSet) {
        String factProcessingSQL = """
            INSERT INTO dwd_apn.dwd_application_fact
            SELECT CAST(talent_recruitment_process_id AS BIGINT),
                   CAST(node_type AS INT),
                   CAST(node_status AS INT),
                   CAST(node_id AS BIGINT),
                   CAST(progress AS INT),
                   CAST(final_round AS BOOLEAN),
                   CAST(add_date AS TIMESTAMP(3)),
                   CAST(event_date AS TIMESTAMP(3)),
                   CAST(DATE_FORMAT(add_date, 'yyyy-MM') AS STRING) AS dt,
                   CAST(operator AS BIGINT)
            FROM (
                     -- Submit to Job (node_type = 10)
                     SELECT CAST(trpn.talent_recruitment_process_id AS BIGINT) AS talent_recruitment_process_id,
                            CAST(trpn.node_type AS INT)                        AS node_type,
                            CAST(trpn.node_status AS INT)                      AS node_status,
                            CAST(submit_job.id AS BIGINT)                      AS node_id,
                            CAST(0 AS INT)                                     AS progress,
                            CAST(TRUE AS BOOLEAN)                              AS final_round,
                            CAST(submit_job.created_date AS TIMESTAMP(3))      AS add_date,
                            CAST(submit_job.created_date AS TIMESTAMP(3))      AS event_date,
                            CAST(submit_job.puser_id AS BIGINT)                AS operator
                     FROM paimon_catalog.ods_apn.talent_recruitment_process_submit_to_job AS submit_job
                              INNER JOIN paimon_catalog.ods_apn.talent_recruitment_process_node AS trpn
                                         ON trpn.talent_recruitment_process_id = submit_job.talent_recruitment_process_id
                                             AND trpn.node_type = 10
            
                     UNION ALL
            
                     -- Submit to Client (node_type = 20)
                     SELECT CAST(trpn.talent_recruitment_process_id AS BIGINT),
                            CAST(trpn.node_type AS INT),
                            CAST(trpn.node_status AS INT),
                            CAST(submit_client.id AS BIGINT),
                            CAST(0 AS INT),
                            CAST(TRUE AS BOOLEAN),
                            CAST(submit_client.created_date AS TIMESTAMP(3)),
                            CAST(submit_client.submit_time AS TIMESTAMP(3)),
                            CAST(submit_client.puser_id AS BIGINT)
                     FROM paimon_catalog.ods_apn.talent_recruitment_process_submit_to_client AS submit_client
                              INNER JOIN paimon_catalog.ods_apn.talent_recruitment_process_node AS trpn
                                         ON trpn.talent_recruitment_process_id = submit_client.talent_recruitment_process_id
                                             AND trpn.node_type = 20
            
                     UNION ALL
            
                     -- Interview (node_type = 30)
                     SELECT CAST(trpn.talent_recruitment_process_id AS BIGINT),
                            CAST(trpn.node_type AS INT),
                            CAST(trpn.node_status AS INT),
                            CAST(interview.id AS BIGINT),
                            CAST(interview.progress AS INT),
                            CAST(interview.final_round AS BOOLEAN),
                            CAST(interview.created_date AS TIMESTAMP(3)),
                            CAST(CONVERT_TZ(DATE_FORMAT(interview.from_time, 'yyyy-MM-dd HH:mm:ss'),
                                            COALESCE(interview.time_zone, 'UTC'), 'UTC') AS TIMESTAMP(3)) AS event_date,
                            CAST(interview.puser_id AS BIGINT)
                     FROM paimon_catalog.ods_apn.talent_recruitment_process_interview AS interview
                              INNER JOIN paimon_catalog.ods_apn.talent_recruitment_process_node AS trpn
                                         ON trpn.talent_recruitment_process_id = interview.talent_recruitment_process_id
                                             AND trpn.node_type = 30
            
                     UNION ALL
            
                     -- Offer (node_type = 40)
                     SELECT CAST(trpn.talent_recruitment_process_id AS BIGINT),
                            CAST(trpn.node_type AS INT),
                            CAST(trpn.node_status AS INT),
                            CAST(offer.id AS BIGINT),
                            CAST(0 AS INT),
                            CAST(TRUE AS BOOLEAN),
                            CAST(offer.created_date AS TIMESTAMP(3)),
                            CAST(offer.created_date AS TIMESTAMP(3)),
                            CAST(offer.puser_id AS BIGINT)
                     FROM paimon_catalog.ods_apn.talent_recruitment_process_offer AS offer
                              INNER JOIN paimon_catalog.ods_apn.talent_recruitment_process_node AS trpn
                                         ON trpn.talent_recruitment_process_id = offer.talent_recruitment_process_id
                                             AND trpn.node_type = 40
            
                     UNION ALL
            
                     -- IPG Offer Accept (node_type = 41)
                     SELECT CAST(trpn.talent_recruitment_process_id AS BIGINT),
                            CAST(trpn.node_type AS INT),
                            CAST(trpn.node_status AS INT),
                            CAST(offer_accept.id AS BIGINT),
                            CAST(0 AS INT),
                            CAST(TRUE AS BOOLEAN),
                            CAST(offer_accept.created_date AS TIMESTAMP(3)),
                            CAST(offer_accept.created_date AS TIMESTAMP(3)),
                            CAST(offer_accept.puser_id AS BIGINT)
                     FROM paimon_catalog.ods_apn.talent_recruitment_process_ipg_offer_accept AS offer_accept
                              INNER JOIN paimon_catalog.ods_apn.talent_recruitment_process_node AS trpn
                                         ON trpn.talent_recruitment_process_id = offer_accept.talent_recruitment_process_id
                                             AND trpn.node_type = 41
            
                     UNION ALL
            
                     -- Onboard (node_type = 60)
                     SELECT CAST(trpn.talent_recruitment_process_id AS BIGINT),
                            CAST(trpn.node_type AS INT),
                            CAST(trpn.node_status AS INT),
                            CAST(onboard.id AS BIGINT),
                            CAST(0 AS INT),
                            CAST(TRUE AS BOOLEAN),
                            CAST(onboard.created_date AS TIMESTAMP(3)),
                            CAST(onboard_date.onboard_date AS TIMESTAMP(3)),
                            CAST(onboard.puser_id AS BIGINT)
                     FROM paimon_catalog.ods_apn.talent_recruitment_process_onboard AS onboard
                              INNER JOIN paimon_catalog.ods_apn.talent_recruitment_process_node AS trpn
                                         ON trpn.talent_recruitment_process_id = onboard.talent_recruitment_process_id
                                             AND trpn.node_type = 60
                              INNER JOIN paimon_catalog.ods_apn.talent_recruitment_process_onboard_date AS onboard_date
                                         ON onboard_date.talent_recruitment_process_id = onboard.talent_recruitment_process_id
            
                     UNION ALL
            
                     -- Eliminate (node_type = -1, node_status = 4)
                     SELECT CAST(eliminate.talent_recruitment_process_id AS BIGINT),
                            CAST(-1 AS INT),
                            CAST(4 AS INT),
                            CAST(eliminate.id AS BIGINT),
                            CAST(0 AS INT),
                            CAST(TRUE AS BOOLEAN),
                            CAST(eliminate.created_date AS TIMESTAMP(3)),
                            CAST(eliminate.created_date AS TIMESTAMP(3)),
                            CAST(eliminate.puser_id AS BIGINT)
                     FROM paimon_catalog.ods_apn.talent_recruitment_process_eliminate AS eliminate) AS t;
            """;

        System.out.println(factProcessingSQL);

        statementSet.addInsertSql(factProcessingSQL);
    }
}

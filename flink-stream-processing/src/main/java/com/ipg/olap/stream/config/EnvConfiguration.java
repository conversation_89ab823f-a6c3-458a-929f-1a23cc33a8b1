package com.ipg.olap.stream.config;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import org.apache.flink.kubernetes.shaded.org.yaml.snakeyaml.error.MissingEnvironmentVariableException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Map;

public class EnvConfiguration {

    private static final Logger log = LoggerFactory.getLogger(EnvConfiguration.class);

    private static final ObjectMapper mapper = new ObjectMapper(new YAMLFactory());

    private static String config = null;

    private static final String ENV_CONFIG_NAME = "env";
    private static final String PAIMON_CONFIG_NAME = "paimon";
    private static final String MYSQL_CONFIG_NAME = "mysql";
    private static final String PAIMON_TABLE_NAME = "paimon_table";
    private static final String CDC_ACTION = "action";

    public static void initConfig(String path) {
       Path configFile = new File(path).toPath();
       if (!configFile.toFile().exists()) {
           throw new IllegalArgumentException("Config file not found: " + path);
       }
       try {
           config = new String(Files.readAllBytes(configFile));
       } catch (IOException e) {
           log.error("Error reading config file ", e);
           throw new RuntimeException(e);
       }
    }

    private static Map<String, Object> readConfigFromEnv() {
        String configYamlStr = config;
        if (configYamlStr == null) {
            throw new MissingEnvironmentVariableException("Environment variable " + ENV_CONFIG_NAME + " not found.");
        }
        try {
            String yamlString = configYamlStr.replace("\\n", "\n");
            return mapper.readValue(yamlString, Map.class);
        } catch (JsonProcessingException e) {
            log.error("Error parsing config YAML ", e);
            throw new RuntimeException(e);
        }
    }

    public static Map<String, String> getPaimonConfig() {
        return getConfig(PAIMON_CONFIG_NAME);
    }

    public static Map<String, String> getMysqlConfig() throws Exception {
        return getConfig(MYSQL_CONFIG_NAME);
    }

    public static Map<String, String> getPaimonTableConfig() {
        return getConfig(PAIMON_TABLE_NAME);
    }

    public static Map<String, String> getCdcActionConfig() {
       return getConfig(CDC_ACTION);
    }

    private static Map<String, String> getConfig(String configName) {
        Map<String, Object> envConfig = readConfigFromEnv();
        if (!envConfig.containsKey(configName)) {
            throw new MissingEnvironmentVariableException("%s configuration not found in environment variable %s".formatted(configName, ENV_CONFIG_NAME));
        }
        Map<String, String> config = (Map<String, String>) envConfig.get(configName);
        if (config.isEmpty()) {
            throw new MissingEnvironmentVariableException("%s configuration is empty in environment variable %s".formatted(configName, ENV_CONFIG_NAME));
        }
        return config;
    }

}

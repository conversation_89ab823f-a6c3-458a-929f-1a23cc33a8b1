package com.ipg.olap.stream.processor;

import org.apache.flink.configuration.Configuration;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

import java.util.stream.Collectors;

/**
 * 抽象表处理器基类
 * 提供表处理器的通用实现，包含标准的源表验证逻辑
 */
public abstract class AbstractTableProcessor implements TableProcessor {
    
    protected final String processorName;
    protected final String targetTableName;
    
    protected AbstractTableProcessor(String processorName, String targetTableName) {
        this.processorName = processorName;
        this.targetTableName = targetTableName;
    }
    
    @Override
    public String getProcessorName() {
        return processorName;
    }
    
    @Override
    public String getTargetTableName() {
        return targetTableName;
    }
    
    @Override
    public void validateSourceTables(StreamTableEnvironment tableEnv) {
        String[] requiredTables = getRequiredSourceTables();
        System.out.println("start validating source tables for " + getProcessorName());
        
        for (String table : requiredTables) {
            try {
                tableEnv.executeSql("DESCRIBE `" + table + "`;");
                System.out.println("✅ source table " + table + " exists");
            } catch (Exception e) {
                System.err.println("❌ source table " + table + " not found, error message: " + e.getMessage());
                throw new RuntimeException("source table " + table + " not found");
            }
        }
        System.out.println(getProcessorName() + " source tables validation finished");
    }
    
    /**
     * 获取标准的 Paimon 表配置
     * @return Paimon 表配置字符串
     */
    protected String getStandardPaimonTableConfig() {
        Configuration config = getDefaultPaimonTableConfig();
        return formatPaimonTableConfig(config);
    }

    protected String formatPaimonTableConfig(Configuration configuration) {
        return configuration.toMap().entrySet().stream()
            .map(entry -> "'%s' = '%s'".formatted(entry.getKey(), entry.getValue()))
            .collect(Collectors.joining(",\n"));
    }

    /**
     * 获取标准的 Paimon 表配置
     * @return Paimon 表配置字符串
     */
    protected Configuration getDefaultPaimonTableConfig() {
        Configuration config = new Configuration();
        config.setString("changelog-producer", "lookup");
        config.setString("write-buffer-size", "128mb");
        config.setString("precommit-compact", "true");
        config.setString("sink.parallelism", "4");
        config.setString("lookup.cache.ttl", "1h");
        return config;
    }

    /**
     * 执行完整的处理流程
     *
     * @param tableEnv     Flink Table Environment
     * @param statementSet
     */
    public final void process(StreamTableEnvironment tableEnv, StatementSet statementSet) {
        System.out.println("start processing: " + getProcessorName());
        
        // 1. 验证源表
        validateSourceTables(tableEnv);
        
        // 2. 创建目标表
        createTargetTable(tableEnv);
        
        // 3. 执行处理逻辑
        executeProcessing(tableEnv, statementSet);
        
        System.out.println(getProcessorName() + "processing finished");
    }
}

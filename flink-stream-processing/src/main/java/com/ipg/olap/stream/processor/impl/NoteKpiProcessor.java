package com.ipg.olap.stream.processor.impl;

import com.ipg.olap.stream.processor.AbstractTableProcessor;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

public class NoteKpiProcessor extends AbstractTableProcessor {

    protected NoteKpiProcessor() {
        super("NoteKpiProcessor", "dwd_apn.dwd_note_kpi");
    }

    @Override
    public void createTargetTable(StreamTableEnvironment tableEnv) {

    }

    @Override
    public void executeProcessing(StreamTableEnvironment tableEnv, StatementSet statementSet) {

    }

    @Override
    public String[] getRequiredSourceTables() {
        return new String[] {
            "talent_note",
            "talent",
            "user",
            "permission_user_team",

        };
    }
}

package com.ipg.olap.stream.processor.impl;

import com.ipg.olap.stream.processor.AbstractTableProcessor;
import org.apache.flink.table.api.StatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;

public class NoteKpiProcessor extends AbstractTableProcessor {

    protected NoteKpiProcessor() {
        super("NoteKpiProcessor", "dwd_apn.dwd_note_kpi");
    }

    @Override
    public void createTargetTable(StreamTableEnvironment tableEnv) {
        String sql = """
            CREATE TABLE IF NOT EXISTS dwd_apn.dwd_note_kpi (
                tenant_id BIGINT,
                team_id BIGINT,
                team_name STRING,
                team_parent_id BIGINT,
                team_level INT,
                team_is_leaf TINYINT,
                user_id BIGINT,
                user_name STRING,
                user_activated TINYINT,
                add_date TIMESTAMP(3),
                callNoteNum ARRAY<BIGINT>,
                personNoteNum ARRAY<BIGINT>,
                otherNoteNum ARRAY<BIGINT>,
                emailNoteNum ARRAY<BIGINT>,
                videoNoteNum ARRAY<BIGINT>,
                iciNum ARRAY<BIGINT>,
                noteCount ARRAY<BIGINT>,
                unique_talent_ids ARRAY<BIGINT>,
                application_note_count_num ARRAY<BIGINT>,
                talent_tracking_note_count_num ARRAY<BIGINT>,
                talent_tracking_note_ids ARRAY<BIGINT>,
                dt STRING,
                PRIMARY KEY (tenant_id, add_date, dt) NOT ENFORCED
            ) PARTITIONED BY (dt)
            WITH (
                %s
            )
            """.formatted(getStandardPaimonTableConfig());

    }

    @Override
    public void executeProcessing(StreamTableEnvironment tableEnv, StatementSet statementSet) {

    }

    @Override
    public String[] getRequiredSourceTables() {
        return new String[] {
            "ods_apn.talent_note",
            "ods_apn.talent",
            "ods_apn.user",
            "ods_apn.permission_user_team",

        };
    }
}

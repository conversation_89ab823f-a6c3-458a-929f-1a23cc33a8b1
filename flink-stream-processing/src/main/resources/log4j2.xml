<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
    <!-- 定义日志输出格式 -->
    <Appenders>
        <!-- 控制台日志输出 -->
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss} %-5level [%t] %logger{36} - %msg%n%throwable"/>
        </Console>

        <!-- 文件日志输出 -->
        <File name="File" fileName="logs/app.log">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss} %-5level [%t] %logger{36} - %msg%n%throwable"/>
        </File>
    </Appenders>

    <!-- 定义日志记录器 -->
    <Loggers>
        <!-- 根日志记录器，设置日志级别和输出方式 -->
        <Root level="info">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="File"/>
        </Root>

        <!-- Flink 相关日志 -->
        <Logger name="org.apache.flink" level="info" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="File"/>
        </Logger>

        <!-- Paimon 相关日志 -->
        <Logger name="org.apache.paimon" level="info" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="File"/>
        </Logger>

        <!-- AWS 相关日志 -->
        <Logger name="com.amazonaws" level="info" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="File"/>
        </Logger>

        <!-- MySQL CDC 相关日志 -->
        <Logger name="com.github.shyiko.mysql.binlog" level="info" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="File"/>
        </Logger>

        <!-- 自定义日志记录器 -->
        <Logger name="com.ipg.olap" level="info" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="File"/>
        </Logger>
    </Loggers>
</Configuration>
name: Build Flink k8s Operator Image And Publish
on:
  push:
    branches:
      - flink-k8s-operator
env:
  IMAGE_REPO: minghealtomni/flink_k8s_operator
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          ref: flink-k8s-operator
      - name: Set up Java
        uses: actions/setup-java@v3
        with:
          java-version: '11'
          distribution: 'adopt'
      - name: Maven Build
        run: mvn clean package
      - name: check file
        run: ls -l
      - name: Set Image Version
        run: echo "IMAGE_NAME=$IMAGE_REPO:$(date +"%Y%m%d%H%M%S")" >> $GITHUB_ENV
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1
      - name: Login to DockerHub
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PASSWORD }}
      - name: Build and push
        uses: docker/build-push-action@v3
        with:
          context: ./flink-operator-plugin
          file: ./flink-operator-plugin/Dockerfile
          push: true
          tags: ${{ env.IMAGE_NAME }}
          cache-from: type=gha
          cache-to: type=gha,mode=min
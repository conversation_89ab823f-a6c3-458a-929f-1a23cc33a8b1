name: Build Flink Job Image And Publish
on:
  push:
    branches:
      - paimon
env:
  IMAGE_REPO: minghealtomni/flink_mysql_paimon
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          ref: paimon
      - name: Set up Java
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'adopt'
      - name: Maven Build
        run: mvn clean package
      - name: check file
        run: |
          ls -l
          ls -l flink-stream-processing/target/
      - name: Set Image Version
        run: echo "IMAGE_NAME=$IMAGE_REPO:$(date +"%Y%m%d%H%M%S")" >> $GITHUB_ENV
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1
      - name: Login to DockerHub
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PASSWORD }}
      - name: Build and push
        uses: docker/build-push-action@v3
        with:
          context: .
          file: ./flink-stream-processing/Dockerfile
          push: true
          tags: ${{ env.IMAGE_NAME }}
          cache-from: type=gha
          cache-to: type=gha,mode=min
CREATE TABLE IF NOT EXISTS `ods_apn`.`time_sheet_expense_week_ending_record`
(
    `assignment_id`        BIGINT         NOT NULL,
    `id`                   BIGINT         NOT NULL,
    `created_date`         DATETIME       NOT NULL DEFAULT "1970-01-01 00:00:00",
    `record_id`            BIGINT         NOT NULL,
    `tenant_id`            BIGINT         NOT NULL,
    `talent_id`            BIGINT         NOT NULL,
    `job_id`               BIGINT         NOT NULL,
    `company_id`           BIGINT         NOT NULL,
    `manager`              VARCHAR(150)   NULL,
    `manager_id`           BIGINT         NULL,
    `am_approver`          VA<PERSON>HA<PERSON>(150)   NULL,
    `am_approver_id`       BIGINT         NULL,
    `primary_manager`      VARCHAR(150)   NULL,
    `primary_manager_id`   BIGINT         NULL,
    `am`                   VARCHAR(12000) NULL,
    `am_ids`               VARCHAR(765)   NULL,
    `client_ids`           VARCHAR(765)   NULL,
    `allow_submit_expense` BO<PERSON><PERSON><PERSON>        NULL,
    `job_title`            VA<PERSON>HA<PERSON>(765)   NULL,
    `company_name`         VA<PERSON><PERSON><PERSON>(765)   NULL,
    `full_name`            VA<PERSON>HA<PERSON>(765)   NULL,
    `approved_date`        DATETIME       NULL,
    `start_date`           DATETIME       NULL,
    `end_date`             DATETIME       NULL,
    `assignment_status`    TINYINT        NULL,
    `work_date`            DATETIME       NULL,
    `week_start`           DATETIME       NULL,
    `week_end`             DATETIME       NULL,
    `week_ending_date`     DATETIME       NULL,
    `cost`                 DECIMAL(10, 2) NULL,
    `submitted_date`       DATETIME       NULL,
    `status`               TINYINT        NULL,
    `employment_category`  TINYINT        NULL,
    `billing_frequency`    TINYINT        NULL,
    `payment_frequency`    TINYINT        NULL,
    `assignment_division`  TINYINT        NULL,
    `expense_type`         TINYINT        NULL,
    `expense_index`        INT            NULL     DEFAULT "0",
    `created_by`           VARCHAR(150)   NOT NULL,
    `last_modified_by`     VARCHAR(150)   NULL,
    `last_modified_date`   DATETIME       NULL
) PRIMARY KEY (`assignment_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`assignment_id`)
ORDER BY (`assignment_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`enum_company_contact_tag`
(
    `id`               BIGINT       NOT NULL,
    `name`             VARCHAR(384) NOT NULL,
    `en_display`       VARCHAR(384) NULL,
    `cn_display`       VARCHAR(384) NULL,
    `en_display_order` INT          NULL,
    `cn_display_order` INT          NULL
) PRIMARY KEY (`id`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`job`
(
    `id`                       BIGINT       NOT NULL,
    `created_date`             DATETIME     NOT NULL DEFAULT "1970-01-01 00:00:00",
    `tenant_id`                BIGINT       NOT NULL,
    `company_id`               BIGINT       NOT NULL,
    `title`                    VARCHAR(765) NOT NULL,
    `start_date`               DATETIME     NULL,
    `end_date`                 DATETIME     NULL,
    `posting_time`             DATETIME     NULL,
    `open_time`                DATETIME     NULL,
    `job_type`                 INT          NULL,
    `status`                   INT          NOT NULL,
    `jd_url`                   VARCHAR(765) NULL,
    `jd_has_display`           BOOLEAN      NULL,
    `flexible_location`        BOOLEAN      NULL,
    `code`                     VARCHAR(765) NULL,
    `is_visible`               TINYINT      NOT NULL DEFAULT "1",
    `openings`                 INT          NULL     DEFAULT "1",
    `max_submissions`          INT          NULL,
    `created_by`               VARCHAR(150) NOT NULL,
    `last_modified_by`         VARCHAR(150) NULL,
    `last_modified_date`       DATETIME     NULL,
    `currency`                 INT          NULL,
    `last_edited_time`         DATETIME     NULL,
    `last_sync_time`           DATETIME     NULL,
    `sync_paused`              INT          NOT NULL DEFAULT "0",
    `puser_id`                 BIGINT       NULL,
    `pteam_id`                 BIGINT       NULL,
    `additional_info_id`       BIGINT       NULL,
    `minimum_degree_id`        BIGINT       NULL,
    `recruitment_process_id`   BIGINT       NOT NULL,
    `enum_priority_id`         TINYINT      NULL,
    `last_non_open_time`       DATETIME     NULL,
    `created_date_los_angeles` DATE         NULL,
    `created_date_shanghai`    DATE         NULL,
    `created_date_new_york`    DATE         NULL,
    `end_date_format`          DATE         NULL,
    `start_date_format`        DATE         NULL,
    `sales_lead_id`            BIGINT       NULL,
    `cooperation_status`       VARCHAR(384) NULL,
    `is_need_sync_hr`          BOOLEAN      NULL     DEFAULT "0",
    `contract_duration`        INT          NULL
) PRIMARY KEY (`id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`start_fte_salary_package`
(
    `start_id`           BIGINT         NOT NULL,
    `id`                 BIGINT         NOT NULL,
    `created_date`       DATETIME       NOT NULL DEFAULT "1970-01-01 00:00:00",
    `salary_type`        INT            NULL,
    `amount`             DECIMAL(20, 2) NULL,
    `need_charge`        BOOLEAN        NOT NULL,
    `created_by`         VARCHAR(150)   NOT NULL,
    `last_modified_by`   VARCHAR(150)   NULL,
    `last_modified_date` DATETIME       NULL,
    `puser_id`           BIGINT         NULL,
    `pteam_id`           BIGINT         NULL
) PRIMARY KEY (`start_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`start_id`)
ORDER BY (`start_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`t_email_attachment_record`
(
    `id`                 BIGINT       NOT NULL,
    `created_date`       DATETIME     NOT NULL DEFAULT "1970-01-01 00:00:00",
    `email_id`           BIGINT       NULL,
    `attachment_url`     VARCHAR(765) NULL,
    `created_by`         VARCHAR(150) NOT NULL,
    `last_modified_by`   VARCHAR(150) NULL,
    `last_modified_date` DATETIME     NULL
) PRIMARY KEY (`id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`start_contract_rate`
(
    `start_id`                        BIGINT         NOT NULL,
    `id`                              BIGINT         NOT NULL,
    `created_date`                    DATETIME       NOT NULL DEFAULT "1970-01-01 00:00:00",
    `tenant_id`                       BIGINT         NOT NULL,
    `extend_start_contract_rate_id`   BIGINT         NULL,
    `start_date`                      DATE           NULL,
    `end_date`                        DATE           NULL,
    `currency`                        INT            NOT NULL,
    `rate_unit_type`                  INT            NULL,
    `final_bill_rate`                 DECIMAL(10, 2) NULL,
    `final_pay_rate`                  DECIMAL(10, 2) NULL,
    `tax_burden_rate`                 VARCHAR(765)   NULL,
    `msp_rate`                        VARCHAR(765)   NULL,
    `immigration_cost`                VARCHAR(765)   NULL,
    `extra_cost`                      DECIMAL(10, 2) NULL,
    `estimated_working_hour_per_week` DECIMAL(10, 2) NULL,
    `total_bill_amount`               DECIMAL(20, 2) NULL,
    `note`                            VARCHAR(15000) NULL,
    `created_by`                      VARCHAR(150)   NOT NULL,
    `last_modified_by`                VARCHAR(150)   NULL,
    `last_modified_date`              DATETIME       NULL,
    `puser_id`                        BIGINT         NULL,
    `pteam_id`                        BIGINT         NULL
) PRIMARY KEY (`start_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`start_id`)
ORDER BY (`start_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`job_job_function_relation`
(
    `job_id`          BIGINT      NOT NULL,
    `id`              BIGINT      NOT NULL,
    `job_function_id` VARCHAR(60) NOT NULL
) PRIMARY KEY (`job_id`, `id`)
DISTRIBUTED BY HASH (`job_id`)
ORDER BY (`job_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_current_location`
(
    `talent_id`          BIGINT           NOT NULL,
    `id`                 BIGINT           NOT NULL,
    `created_date`       DATETIME         NOT NULL DEFAULT "1970-01-01 00:00:00",
    `official_county`    VARCHAR(600)     NULL,
    `official_country`   VARCHAR(600)     NULL,
    `official_city`      VARCHAR(600)     NULL,
    `official_province`  VARCHAR(600)     NULL,
    `zip_code`           VARCHAR(96)      NULL,
    `original_loc`       VARCHAR(1048576) NULL,
    `created_by`         VARCHAR(150)     NOT NULL DEFAULT "",
    `last_modified_by`   VARCHAR(150)     NULL,
    `last_modified_date` DATETIME         NULL
) PRIMARY KEY (`talent_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`talent_id`)
ORDER BY (`talent_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`enum_language`
(
    `id`               INT              NOT NULL,
    `name`             CHAR(192)        NULL,
    `pattern`          VARCHAR(1048576) NULL,
    `code`             CHAR(24)         NULL,
    `cn_display`       CHAR(192)        NULL,
    `en_display`       CHAR(192)        NULL,
    `en_display_order` INT              NULL,
    `cn_display_order` INT              NULL,
    `en_sort_order`    INT              NULL,
    `cn_sort_order`    INT              NULL
) PRIMARY KEY (`id`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`time_sheet_record`
(
    `assignment_id`      BIGINT         NOT NULL,
    `id`                 BIGINT         NOT NULL,
    `created_date`       DATETIME       NOT NULL DEFAULT "1970-01-01 00:00:00",
    `tenant_id`          BIGINT         NOT NULL,
    `talent_id`          BIGINT         NOT NULL,
    `work_date`          DATETIME       NULL,
    `work_hours`         DECIMAL(10, 2) NULL,
    `regular_hours`      DECIMAL(10, 2) NULL,
    `over_time`          DECIMAL(10, 2) NULL,
    `double_time`        DECIMAL(10, 2) NULL,
    `week_day`           VARCHAR(765)   NULL,
    `total_hours`        DECIMAL(10, 2) NULL,
    `week_ending_date`   DATETIME       NULL,
    `week_start`         DATETIME       NULL,
    `week_end`           DATETIME       NULL,
    `submitted_date`     DATETIME       NULL,
    `status`             TINYINT        NULL,
    `time_sheet_type`    TINYINT        NULL,
    `created_by`         VARCHAR(150)   NOT NULL,
    `last_modified_by`   VARCHAR(150)   NULL,
    `last_modified_date` DATETIME       NULL,
    `week_id`            BIGINT         NULL
) PRIMARY KEY (`assignment_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`assignment_id`)
ORDER BY (`assignment_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_recruitment_process_ipg_offer_accept`
(
    `talent_recruitment_process_id` BIGINT           NOT NULL,
    `id`                            BIGINT           NOT NULL,
    `created_date`                  DATETIME         NOT NULL DEFAULT "1970-01-01 00:00:00",
    `note`                          VARCHAR(1048576) NULL,
    `created_by`                    VARCHAR(150)     NOT NULL,
    `last_modified_by`              VARCHAR(150)     NULL,
    `last_modified_date`            DATETIME         NULL,
    `puser_id`                      BIGINT           NULL,
    `pteam_id`                      BIGINT           NULL,
    `note_last_modified_by_user_id` BIGINT           NULL,
    `note_last_modified_date`       DATETIME         NULL,
    `created_date_los_angeles`      DATE             NULL,
    `created_date_shanghai`         DATE             NULL,
    `created_date_new_york`         DATE             NULL,
    `last_update_user_id`           BIGINT           NULL,
    `channel_platform`              INT              NULL,
    `profit_sharing_ratio`          DECIMAL(10, 2)   NULL
) PRIMARY KEY (`talent_recruitment_process_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`talent_recruitment_process_id`)
ORDER BY (`talent_recruitment_process_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`company`
(
    `id`                 BIGINT        NOT NULL,
    `created_date`       DATETIME      NOT NULL DEFAULT "1970-01-01 00:00:00",
    `logo`               VARCHAR(765)  NULL,
    `full_business_name` VARCHAR(3000) NOT NULL,
    `client_level_type`  TINYINT       NULL,
    `active`             TINYINT       NULL,
    `tenant_id`          BIGINT        NULL,
    `created_by`         VARCHAR(150)  NOT NULL,
    `last_modified_by`   VARCHAR(150)  NULL,
    `last_modified_date` DATETIME      NULL,
    `puser_id`           BIGINT        NULL,
    `pteam_id`           BIGINT        NULL,
    `last_sync_time`     DATETIME      NULL,
    `last_edited_time`   DATETIME      NULL,
    `is_need_sync_hr`    BOOLEAN       NULL     DEFAULT "0",
    `request_date`       DATETIME      NULL
) PRIMARY KEY (`id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`start`
(
    `id`                            BIGINT           NOT NULL,
    `created_date`                  DATETIME         NOT NULL DEFAULT "1970-01-01 00:00:00",
    `tenant_id`                     BIGINT           NULL     DEFAULT "0",
    `talent_id`                     BIGINT           NULL,
    `job_id`                        BIGINT           NULL,
    `company_id`                    BIGINT           NULL,
    `talent_recruitment_process_id` BIGINT           NOT NULL,
    `client_contact_id`             BIGINT           NULL,
    `start_date`                    DATE             NULL,
    `end_date`                      DATE             NULL,
    `warranty_end_date`             DATE             NULL,
    `position_type`                 INT              NOT NULL,
    `time_zone`                     VARCHAR(765)     NULL,
    `note`                          VARCHAR(1048576) NULL,
    `last_edited_time`              DATETIME         NOT NULL DEFAULT "1970-01-01 00:00:00",
    `last_sync_time`                DATETIME         NULL,
    `sync_paused`                   INT              NOT NULL DEFAULT "0",
    `status`                        INT              NOT NULL,
    `start_type`                    INT              NOT NULL DEFAULT "0",
    `created_by`                    VARCHAR(150)     NOT NULL,
    `last_modified_by`              VARCHAR(150)     NULL,
    `last_modified_date`            DATETIME         NULL,
    `currency`                      INT              NULL,
    `talent_name`                   VARCHAR(765)     NULL,
    `job_title`                     VARCHAR(765)     NULL,
    `company`                       VARCHAR(765)     NULL,
    `puser_id`                      BIGINT           NULL,
    `pteam_id`                      BIGINT           NULL,
    `charge_number`                 VARCHAR(765)     NULL,
    `tvc_number`                    VARCHAR(765)     NULL,
    `corp_to_corp`                  BOOLEAN          NULL     DEFAULT "0",
    `working_mode`                  INT              NULL,
    `channel_platform`              INT              NULL,
    `profit_sharing_ratio`          DECIMAL(10, 2)   NULL,
    `is_substitute_talent`          TINYINT          NULL     DEFAULT "0",
    `relation_process_id`           BIGINT           NULL,
    `substitute_talent_id`          BIGINT           NULL
) PRIMARY KEY (`id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_industry_relation`
(
    `id`                 BIGINT       NOT NULL,
    `created_date`       DATETIME     NOT NULL DEFAULT "1970-01-01 00:00:00",
    `talent_id`          BIGINT       NULL,
    `industry_id`        INT          NOT NULL,
    `created_by`         VARCHAR(150) NOT NULL DEFAULT "",
    `last_modified_by`   VARCHAR(150) NULL,
    `last_modified_date` DATETIME     NULL,
    `puser_id`           BIGINT       NULL,
    `pteam_id`           BIGINT       NULL
) PRIMARY KEY (`id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`user`
(
    `id`                                       BIGINT        NOT NULL,
    `created_date`                             DATETIME      NOT NULL DEFAULT "1970-01-01 00:00:00",
    `uid`                                      VARCHAR(765)  NULL,
    `username`                                 VARCHAR(300)  NULL,
    `email`                                    VARCHAR(300)  NOT NULL,
    `alternate_email`                          VARCHAR(300)  NULL,
    `password_hash`                            VARCHAR(180)  NULL,
    `first_name`                               VARCHAR(150)  NOT NULL,
    `last_name`                                VARCHAR(150)  NOT NULL,
    `phone`                                    VARCHAR(765)  NULL,
    `tenant_id`                                BIGINT        NOT NULL,
    `image_url`                                VARCHAR(768)  NULL,
    `activated`                                BOOLEAN       NOT NULL,
    `lang_key`                                 VARCHAR(15)   NULL,
    `activation_key`                           VARCHAR(60)   NULL,
    `reset_key`                                VARCHAR(60)   NULL,
    `note`                                     VARCHAR(3000) NULL,
    `job_title`                                VARCHAR(765)  NULL,
    `credit`                                   INT           NULL     DEFAULT "0",
    `bulk_credit`                              INT           NULL     DEFAULT "0",
    `created_by`                               VARCHAR(150)  NOT NULL,
    `reset_date`                               DATETIME      NULL,
    `last_modified_by`                         VARCHAR(150)  NULL,
    `last_modified_date`                       DATETIME      NULL,
    `last_sync_time`                           DATETIME      NULL,
    `data_scope`                               INT           NULL     DEFAULT "1",
    `client_contact_data_scope`                TINYINT       NULL     DEFAULT "1",
    `report_data_scope`                        TINYINT       NULL     DEFAULT "1",
    `custom_timezone`                          VARCHAR(150)  NULL,
    `sync_lark`                                BOOLEAN       NULL     DEFAULT "0",
    `cancellation_time`                        DATETIME      NULL,
    `home_and_calendar_data_scope`             TINYINT       NULL     DEFAULT "1",
    `candidate_pipeline_management_data_scope` TINYINT       NULL     DEFAULT "1",
    `enum_level_of_experience_id`              BIGINT        NULL,
    `china_invoicing_data_scope`               TINYINT       NULL     DEFAULT "1"
) PRIMARY KEY (`id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`permission_extra_user_team`
(
    `id`                 BIGINT       NOT NULL,
    `created_date`       DATETIME     NOT NULL DEFAULT "1970-01-01 00:00:00",
    `user_id`            BIGINT       NULL,
    `module`             TINYINT      NULL     DEFAULT "1",
    `team_id`            BIGINT       NULL,
    `tenant_id`          BIGINT       NULL,
    `writable`           TINYINT      NULL,
    `created_by`         VARCHAR(150) NULL,
    `last_modified_by`   VARCHAR(150) NULL,
    `last_modified_date` DATETIME     NULL
) PRIMARY KEY (`id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`timesheet_manager`
(
    `assignment_id` BIGINT  NOT NULL,
    `id`            BIGINT  NOT NULL,
    `talent_id`     BIGINT  NOT NULL,
    `client_id`     BIGINT  NOT NULL,
    `role`          TINYINT NOT NULL
) PRIMARY KEY (`assignment_id`, `id`)
DISTRIBUTED BY HASH (`assignment_id`)
ORDER BY (`assignment_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`resume`
(
    `id`                 BIGINT           NOT NULL,
    `created_date`       DATETIME         NOT NULL DEFAULT "1970-01-01 00:00:00",
    `uuid`               VARCHAR(765)     NOT NULL,
    `text`               VARCHAR(1048576) NULL,
    `parse_result`       VARCHAR(1048576) NULL,
    `has_portrait`       BOOLEAN          NULL,
    `has_display`        BOOLEAN          NULL,
    `n_pages`            INT              NULL,
    `created_by`         VARCHAR(150)     NOT NULL,
    `last_modified_by`   VARCHAR(150)     NULL,
    `last_modified_date` DATETIME         NULL,
    `skills_text`        VARCHAR(1048576) NULL,
    `data_md5`           VARCHAR(765)     NULL
) PRIMARY KEY (`id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_recruitment_process_ipg_agreed_pay_rate`
(
    `talent_recruitment_process_id` BIGINT         NOT NULL,
    `id`                            BIGINT         NOT NULL,
    `created_date`                  DATETIME       NOT NULL DEFAULT "1970-01-01 00:00:00",
    `currency`                      INT            NOT NULL,
    `rate_unit_type`                INT            NOT NULL,
    `agreed_pay_rate`               DECIMAL(20, 2) NOT NULL,
    `created_by`                    VARCHAR(150)   NOT NULL,
    `last_modified_by`              VARCHAR(150)   NULL,
    `last_modified_date`            DATETIME       NULL,
    `puser_id`                      BIGINT         NULL,
    `pteam_id`                      BIGINT         NULL
) PRIMARY KEY (`talent_recruitment_process_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`talent_recruitment_process_id`)
ORDER BY (`talent_recruitment_process_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_contact`
(
    `talent_id`           BIGINT           NOT NULL,
    `id`                  BIGINT           NOT NULL,
    `created_date`        DATETIME         NOT NULL DEFAULT "1970-01-01 00:00:00",
    `jhi_type`            INT              NOT NULL,
    `contact`             VARCHAR(765)     NOT NULL,
    `verified`            BOOLEAN          NULL     DEFAULT "0",
    `details`             VARCHAR(1200)    NULL,
    `info`                VARCHAR(1048576) NULL,
    `tenant_id`           BIGINT           NOT NULL,
    `status`              INT              NOT NULL DEFAULT "0",
    `verification_status` TINYINT          NULL,
    `created_by`          VARCHAR(150)     NOT NULL,
    `last_modified_by`    VARCHAR(150)     NULL,
    `last_modified_date`  DATETIME         NULL,
    `sort`                INT              NULL,
    `puser_id`            BIGINT           NULL,
    `pteam_id`            BIGINT           NULL
) PRIMARY KEY (`talent_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`talent_id`)
ORDER BY (`talent_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`user_job_relation`
(
    `user_id`            BIGINT           NOT NULL,
    `id`                 BIGINT           NOT NULL,
    `permission`         INT              NULL,
    `search_string`      VARCHAR(1048576) NULL,
    `job_id`             BIGINT           NOT NULL,
    `created_by`         VARCHAR(150)     NOT NULL,
    `created_date`       DATETIME         NOT NULL DEFAULT "1970-01-01 00:00:00",
    `last_modified_by`   VARCHAR(150)     NULL,
    `last_modified_date` DATETIME         NULL
) PRIMARY KEY (`user_id`, `id`)
DISTRIBUTED BY HASH (`user_id`)
ORDER BY (`user_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_recruitment_process`
(
    `id`                     BIGINT           NOT NULL,
    `created_date`           DATETIME         NOT NULL DEFAULT "1970-01-01 00:00:00",
    `recruitment_process_id` BIGINT           NOT NULL,
    `tenant_id`              BIGINT           NOT NULL,
    `talent_id`              BIGINT           NOT NULL,
    `job_id`                 BIGINT           NOT NULL,
    `note`                   VARCHAR(1048576) NULL,
    `created_by`             VARCHAR(150)     NOT NULL,
    `last_modified_by`       VARCHAR(150)     NULL,
    `last_modified_date`     DATETIME         NULL,
    `puser_id`               BIGINT           NULL,
    `pteam_id`               BIGINT           NULL,
    `last_update_user_id`    BIGINT           NULL,
    `is_substitute_talent`   TINYINT          NULL     DEFAULT "0"
) PRIMARY KEY (`id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`invoice_activity`
(
    `id`                    BIGINT         NOT NULL,
    `created_date`          DATETIME       NOT NULL DEFAULT "1970-01-01 00:00:00",
    `invoice_id`            BIGINT         NULL,
    `invoice_no`            VARCHAR(150)   NULL,
    `invoice_activity_type` INT            NULL,
    `status`                INT            NULL,
    `user_id`               BIGINT         NULL,
    `user_full_name`        VARCHAR(765)   NULL,
    `amount`                DECIMAL(10, 2) NULL,
    `note`                  VARCHAR(3000)  NULL,
    `payment_id`            BIGINT         NULL,
    `created_by`            VARCHAR(150)   NULL,
    `last_modified_by`      VARCHAR(150)   NULL,
    `last_modified_date`    DATETIME       NULL,
    `puser_id`              BIGINT         NULL,
    `pteam_id`              BIGINT         NULL
) PRIMARY KEY (`id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`recruitment_process`
(
    `id`                       BIGINT        NOT NULL,
    `created_date`             DATETIME      NOT NULL DEFAULT "1970-01-01 00:00:00",
    `recruitment_process_type` INT           NOT NULL DEFAULT "2",
    `tenant_id`                BIGINT        NOT NULL,
    `job_type`                 INT           NULL,
    `name`                     VARCHAR(765)  NOT NULL,
    `description`              VARCHAR(1500) NULL,
    `status`                   INT           NOT NULL,
    `created_by`               VARCHAR(150)  NOT NULL,
    `last_modified_by`         VARCHAR(150)  NULL,
    `last_modified_date`       DATETIME      NULL,
    `is_substitute_talent`     TINYINT       NULL     DEFAULT "0"
) PRIMARY KEY (`id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`invoice_client_credit`
(
    `id`                 BIGINT         NOT NULL,
    `created_date`       DATETIME       NOT NULL DEFAULT "1970-01-01 00:00:00",
    `company_id`         BIGINT         NOT NULL,
    `balance`            DECIMAL(10, 2) NOT NULL,
    `created_by`         VARCHAR(150)   NULL,
    `last_modified_by`   VARCHAR(150)   NULL,
    `last_modified_date` DATETIME       NULL,
    `puser_id`           BIGINT         NULL,
    `pteam_id`           BIGINT         NULL,
    `currency`           INT            NOT NULL
) PRIMARY KEY (`id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_recruitment_process_resignation`
(
    `talent_recruitment_process_id` BIGINT           NOT NULL,
    `id`                            BIGINT           NOT NULL,
    `created_date`                  DATETIME         NOT NULL DEFAULT "1970-01-01 00:00:00",
    `resign_date`                   DATE             NOT NULL,
    `reason`                        TINYINT          NOT NULL,
    `note`                          VARCHAR(1048576) NULL,
    `created_by`                    VARCHAR(150)     NOT NULL,
    `last_modified_by`              VARCHAR(150)     NULL,
    `last_modified_date`            DATETIME         NULL,
    `puser_id`                      BIGINT           NULL,
    `pteam_id`                      BIGINT           NULL
) PRIMARY KEY (`talent_recruitment_process_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`talent_recruitment_process_id`)
ORDER BY (`talent_recruitment_process_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`company_location`
(
    `company_id`        BIGINT           NOT NULL,
    `id`                BIGINT           NOT NULL,
    `official_country`  VARCHAR(600)     NULL,
    `official_county`   VARCHAR(600)     NULL,
    `official_province` VARCHAR(600)     NULL,
    `official_city`     VARCHAR(600)     NULL,
    `original_loc`      VARCHAR(1048576) NULL
) PRIMARY KEY (`company_id`, `id`)
DISTRIBUTED BY HASH (`company_id`)
ORDER BY (`company_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`recruitment_process_node`
(
    `id`                     BIGINT        NOT NULL,
    `created_date`           DATETIME      NOT NULL DEFAULT "1970-01-01 00:00:00",
    `recruitment_process_id` BIGINT        NOT NULL,
    `tenant_id`              BIGINT        NOT NULL,
    `node_type`              INT           NOT NULL,
    `name`                   VARCHAR(765)  NOT NULL,
    `description`            VARCHAR(1500) NULL,
    `next_node_id`           BIGINT        NULL,
    `created_by`             VARCHAR(150)  NOT NULL,
    `last_modified_by`       VARCHAR(150)  NULL,
    `last_modified_date`     DATETIME      NULL
) PRIMARY KEY (`id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`enum_company_client_level`
(
    `id`               INT          NOT NULL,
    `name`             VARCHAR(384) NULL,
    `en_display`       VARCHAR(384) NULL,
    `cn_display`       VARCHAR(384) NULL,
    `en_sort_order`    INT          NULL,
    `cn_sort_order`    INT          NULL,
    `en_display_order` INT          NULL,
    `cn_display_order` INT          NULL
) PRIMARY KEY (`id`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_recruitment_process_auto_elimination`
(
    `job_id`             BIGINT       NOT NULL,
    `id`                 BIGINT       NOT NULL,
    `created_date`       DATETIME     NOT NULL DEFAULT "1970-01-01 00:00:00",
    `auto`               BOOLEAN      NULL,
    `period`             INT          NULL,
    `created_by`         VARCHAR(150) NOT NULL,
    `last_modified_by`   VARCHAR(150) NULL,
    `last_modified_date` DATETIME     NULL
) PRIMARY KEY (`job_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`job_id`)
ORDER BY (`job_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`timesheet_google_holiday`
(
    `id`                 BIGINT       NOT NULL,
    `created_date`       DATETIME     NOT NULL,
    `tenant_id`          BIGINT       NULL,
    `company_id`         BIGINT       NULL,
    `holiday_day`        DATE         NULL,
    `hours`              DOUBLE       NULL,
    `created_by`         VARCHAR(150) NULL,
    `last_modified_by`   VARCHAR(150) NULL,
    `last_modified_date` DATETIME     NULL
) PRIMARY KEY (`id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`assignment_pay_rate`
(
    `assignment_id` BIGINT         NOT NULL,
    `id`            BIGINT         NOT NULL,
    `pay_rate`      DECIMAL(10, 2) NULL DEFAULT "0.00",
    `currency`      TINYINT        NOT NULL,
    `time_unit`     TINYINT        NOT NULL,
    `pay_type`      TINYINT        NOT NULL,
    `content_type`  TINYINT        NOT NULL
) PRIMARY KEY (`assignment_id`, `id`)
DISTRIBUTED BY HASH (`assignment_id`)
ORDER BY (`assignment_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`company_sales_lead_client_contact`
(
    `company_id`         BIGINT       NOT NULL,
    `id`                 BIGINT       NOT NULL,
    `created_date`       DATETIME     NOT NULL DEFAULT "1970-01-01 00:00:00",
    `contact_category`   INT          NULL,
    `status`             BOOLEAN      NULL,
    `last_contact_date`  DATETIME     NULL,
    `tenant_id`          BIGINT       NULL,
    `created_by`         VARCHAR(150) NOT NULL,
    `last_modified_by`   VARCHAR(150) NULL,
    `last_modified_date` DATETIME     NULL,
    `receive_email`      BOOLEAN      NULL,
    `approver_id`        BIGINT       NULL,
    `inactived`          BOOLEAN      NULL,
    `zipcode`            VARCHAR(765) NULL,
    `es_id`              VARCHAR(765) NULL,
    `crm_contact_id`     BIGINT       NULL,
    `puser_id`           BIGINT       NULL,
    `pteam_id`           BIGINT       NULL,
    `talent_id`          BIGINT       NOT NULL,
    `is_key_contact`     BOOLEAN      NOT NULL DEFAULT "0"
) PRIMARY KEY (`company_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`company_id`)
ORDER BY (`company_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_recruitment_process_ipg_contract_fee_charge`
(
    `talent_recruitment_process_id`   BIGINT         NOT NULL,
    `id`                              BIGINT         NOT NULL,
    `created_date`                    DATETIME       NOT NULL DEFAULT "1970-01-01 00:00:00",
    `final_bill_rate`                 DECIMAL(20, 2) NOT NULL,
    `final_pay_rate`                  DECIMAL(20, 2) NOT NULL,
    `tax_burden_rate`                 VARCHAR(300)   NOT NULL,
    `msp_rate`                        VARCHAR(300)   NOT NULL,
    `immigration_cost`                VARCHAR(300)   NOT NULL,
    `extra_cost`                      DECIMAL(20, 2) NULL,
    `estimated_working_hour_per_week` DECIMAL(10, 2) NULL,
    `gp`                              DECIMAL(20, 2) NULL,
    `created_by`                      VARCHAR(150)   NOT NULL,
    `last_modified_by`                VARCHAR(150)   NULL,
    `last_modified_date`              DATETIME       NULL,
    `puser_id`                        BIGINT         NULL,
    `pteam_id`                        BIGINT         NULL
) PRIMARY KEY (`talent_recruitment_process_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`talent_recruitment_process_id`)
ORDER BY (`talent_recruitment_process_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`assignment_location`
(
    `assignment_id`    BIGINT       NOT NULL,
    `id`               BIGINT       NOT NULL,
    `city`             VARCHAR(765) NULL DEFAULT "",
    `province`         VARCHAR(765) NULL DEFAULT "",
    `province_code`    VARCHAR(9)   NULL,
    `country`          VARCHAR(765) NULL DEFAULT "",
    `country_code`     VARCHAR(765) NULL DEFAULT "",
    `detailed_address` VARCHAR(765) NULL DEFAULT "",
    `zip_code`         VARCHAR(765) NULL DEFAULT "",
    `time_zone`        VARCHAR(765) NULL DEFAULT ""
) PRIMARY KEY (`assignment_id`, `id`)
DISTRIBUTED BY HASH (`assignment_id`)
ORDER BY (`assignment_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`t_invoice_expense_info`
(
    `invoice_id`         BIGINT         NOT NULL,
    `id`                 BIGINT         NOT NULL,
    `created_date`       DATETIME       NOT NULL DEFAULT "1970-01-01 00:00:00",
    `currency_type`      INT            NULL,
    `expense_category`   TINYINT        NULL,
    `expense_date`       DATE           NULL,
    `expense_amount`     DECIMAL(10, 2) NULL,
    `sort_index`         INT            NULL,
    `expense_index`      INT            NULL     DEFAULT "0",
    `week_day`           VARCHAR(192)   NULL,
    `work_date`          DATETIME       NULL,
    `created_by`         VARCHAR(150)   NOT NULL,
    `last_modified_by`   VARCHAR(150)   NULL,
    `last_modified_date` DATETIME       NULL
) PRIMARY KEY (`invoice_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`invoice_id`)
ORDER BY (`invoice_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`start_residential_address`
(
    `start_id`           BIGINT           NOT NULL,
    `id`                 BIGINT           NOT NULL,
    `created_date`       DATETIME         NOT NULL DEFAULT "1970-01-01 00:00:00",
    `address`            VARCHAR(765)     NULL,
    `address_2`          VARCHAR(765)     NULL,
    `city`               VARCHAR(765)     NULL,
    `city_id`            BIGINT           NULL,
    `province`           VARCHAR(765)     NULL,
    `country`            VARCHAR(765)     NULL,
    `zipcode`            VARCHAR(765)     NULL,
    `location`           VARCHAR(900)     NULL,
    `created_by`         VARCHAR(150)     NOT NULL,
    `last_modified_by`   VARCHAR(150)     NULL,
    `last_modified_date` DATETIME         NULL,
    `puser_id`           BIGINT           NULL,
    `pteam_id`           BIGINT           NULL,
    `original_loc`       VARCHAR(1048576) NULL,
    `address_line`       VARCHAR(765)     NULL
) PRIMARY KEY (`start_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`start_id`)
ORDER BY (`start_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_user_relation`
(
    `talent_id` BIGINT  NOT NULL,
    `id`        BIGINT  NOT NULL,
    `user_id`   BIGINT  NOT NULL,
    `role`      TINYINT NOT NULL
) PRIMARY KEY (`talent_id`, `id`)
DISTRIBUTED BY HASH (`talent_id`)
ORDER BY (`talent_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`enum_timezone`
(
    `id`              INT          NOT NULL,
    `timezone`        VARCHAR(300) NOT NULL,
    `standard_offset` VARCHAR(30)  NOT NULL,
    `daylight_offset` VARCHAR(30)  NOT NULL,
    `suffix`          VARCHAR(60)  NOT NULL,
    `parent`          INT          NOT NULL
) PRIMARY KEY (`id`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_ownership`
(
    `talent_id`                     BIGINT       NOT NULL,
    `id`                            BIGINT       NOT NULL,
    `created_date`                  DATETIME     NOT NULL DEFAULT "1970-01-01 00:00:00",
    `user_id`                       BIGINT       NOT NULL,
    `ownership_type`                INT          NOT NULL,
    `user_role`                     INT          NULL,
    `expire_time`                   DATETIME     NOT NULL DEFAULT "1970-01-01 00:00:00",
    `talent_recruitment_process_id` BIGINT       NULL,
    `created_by`                    VARCHAR(150) NOT NULL,
    `last_modified_by`              VARCHAR(150) NULL,
    `last_modified_date`            DATETIME     NULL,
    `puser_id`                      BIGINT       NULL,
    `pteam_id`                      BIGINT       NULL,
    `tenant_id`                     BIGINT       NULL,
    `auto_assigned`                 BOOLEAN      NULL
) PRIMARY KEY (`talent_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`talent_id`)
ORDER BY (`talent_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_job_function_relation`
(
    `id`                 BIGINT       NOT NULL,
    `created_date`       DATETIME     NOT NULL DEFAULT "1970-01-01 00:00:00",
    `talent_id`          BIGINT       NULL,
    `job_function_id`    INT          NOT NULL,
    `created_by`         VARCHAR(150) NOT NULL DEFAULT "",
    `last_modified_by`   VARCHAR(150) NULL,
    `last_modified_date` DATETIME     NULL
) PRIMARY KEY (`id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_note`
(
    `talent_id`                BIGINT           NOT NULL,
    `id`                       BIGINT           NOT NULL,
    `created_date`             DATETIME         NOT NULL DEFAULT "1970-01-01 00:00:00",
    `title`                    VARCHAR(765)     NULL,
    `note`                     VARCHAR(1048576) NULL,
    `additional_info`          VARCHAR(1048576) NULL,
    `visible`                  BOOLEAN          NULL,
    `priority`                 INT              NULL,
    `user_id`                  BIGINT           NOT NULL,
    `agency_id`                BIGINT           NULL,
    `created_by`               VARCHAR(150)     NOT NULL,
    `last_modified_by`         VARCHAR(150)     NULL,
    `last_modified_date`       DATETIME         NULL,
    `note_type`                TINYINT          NULL,
    `note_status`              TINYINT          NULL,
    `puser_id`                 BIGINT           NULL,
    `pteam_id`                 BIGINT           NULL,
    `last_sync_time`           DATETIME         NULL,
    `created_date_los_angeles` DATE             NULL,
    `created_date_shanghai`    DATE             NULL,
    `created_date_new_york`    DATE             NULL,
    `last_update_user_id`      BIGINT           NULL,
    `read_status`              BOOLEAN          NULL     DEFAULT "1",
    `voip_phone_call_id`       VARCHAR(765)     NULL
) PRIMARY KEY (`talent_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`talent_id`)
ORDER BY (`talent_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`enum_company_sale_lead_source`
(
    `id`               BIGINT       NOT NULL,
    `name`             VARCHAR(384) NOT NULL,
    `en_display`       VARCHAR(384) NULL,
    `cn_display`       VARCHAR(384) NULL,
    `en_display_order` INT          NULL,
    `cn_display_order` INT          NULL
) PRIMARY KEY (`id`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_tracking_note`
(
    `id`                  BIGINT         NOT NULL,
    `created_date`        DATETIME       NOT NULL DEFAULT "1970-01-01 00:00:00",
    `tenant_id`           BIGINT         NOT NULL,
    `user_id`             BIGINT         NOT NULL,
    `tracking_platform`   INT            NOT NULL,
    `platform_id`         VARCHAR(300)   NOT NULL,
    `note`                VARCHAR(15000) NULL,
    `synced_talent_id`    BIGINT         NULL,
    `created_by`          VARCHAR(150)   NOT NULL,
    `last_modified_by`    VARCHAR(150)   NULL,
    `last_modified_date`  DATETIME       NULL,
    `last_update_user_id` BIGINT         NULL
) PRIMARY KEY (`id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`enum_receiving_account`
(
    `id`               BIGINT           NOT NULL,
    `en_display`       VARCHAR(384)     NOT NULL,
    `account_name`     VARCHAR(384)     NULL,
    `currency`         INT              NOT NULL,
    `account_info`     VARCHAR(1048576) NULL,
    `en_display_order` INT              NULL
) PRIMARY KEY (`id`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`timesheet_approve_record`
(
    `id`                 BIGINT           NOT NULL,
    `created_date`       DATETIME         NOT NULL DEFAULT "1970-01-01 00:00:00",
    `tenant_id`          BIGINT           NOT NULL,
    `record_id`          BIGINT           NOT NULL,
    `operator_id`        BIGINT           NULL,
    `week_end`           DATETIME         NULL,
    `assignment_id`      BIGINT           NULL,
    `role`               TINYINT          NOT NULL,
    `status`             TINYINT          NOT NULL,
    `record_type`        TINYINT          NOT NULL,
    `opinion`            VARCHAR(1048576) NULL,
    `created_by`         VARCHAR(150)     NOT NULL,
    `last_modified_by`   VARCHAR(150)     NULL,
    `last_modified_date` DATETIME         NULL
) PRIMARY KEY (`id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`enum_gender`
(
    `id`            INT       NOT NULL,
    `name`          CHAR(192) NULL,
    `en_display`    CHAR(192) NULL,
    `cn_display`    CHAR(192) NULL,
    `display_order` INT       NULL
) PRIMARY KEY (`id`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`start_termination`
(
    `start_id`                  BIGINT         NOT NULL,
    `id`                        BIGINT         NOT NULL,
    `created_date`              DATETIME       NOT NULL DEFAULT "1970-01-01 00:00:00",
    `start_end_date`            DATE           NOT NULL,
    `termination_date`          DATE           NOT NULL,
    `reason`                    INT            NULL,
    `reason_comments`           VARCHAR(1500)  NULL,
    `convert_to_fte_fee_status` INT            NULL,
    `note`                      VARCHAR(15000) NULL,
    `mark_candidate_available`  BOOLEAN        NULL,
    `created_by`                VARCHAR(150)   NOT NULL,
    `last_modified_by`          VARCHAR(150)   NULL,
    `last_modified_date`        DATETIME       NULL,
    `start_status`              INT            NOT NULL,
    `puser_id`                  BIGINT         NULL,
    `pteam_id`                  BIGINT         NULL
) PRIMARY KEY (`start_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`start_id`)
ORDER BY (`start_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_work_authorization_relation`
(
    `talent_id`             BIGINT       NOT NULL,
    `id`                    BIGINT       NOT NULL,
    `created_date`          DATETIME     NOT NULL DEFAULT "1970-01-01 00:00:00",
    `work_authorization_id` INT          NOT NULL,
    `created_by`            VARCHAR(150) NOT NULL DEFAULT "",
    `last_modified_by`      VARCHAR(150) NULL,
    `last_modified_date`    DATETIME     NULL
) PRIMARY KEY (`talent_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`talent_id`)
ORDER BY (`talent_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`start_failed_warranty`
(
    `start_id`                 BIGINT         NOT NULL,
    `id`                       BIGINT         NOT NULL,
    `created_date`             DATETIME       NOT NULL DEFAULT "1970-01-01 00:00:00",
    `end_date`                 DATE           NULL,
    `reason`                   INT            NULL,
    `action_plan`              INT            NULL,
    `total_bill_amount`        DECIMAL(10, 2) NULL,
    `mark_candidate_available` BOOLEAN        NULL,
    `note`                     VARCHAR(15000) NULL,
    `created_by`               VARCHAR(150)   NOT NULL,
    `last_modified_by`         VARCHAR(150)   NULL,
    `last_modified_date`       DATETIME       NULL,
    `puser_id`                 BIGINT         NULL,
    `pteam_id`                 BIGINT         NULL
) PRIMARY KEY (`start_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`start_id`)
ORDER BY (`start_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_recruitment_process_note`
(
    `talent_recruitment_process_id` BIGINT           NOT NULL,
    `id`                            BIGINT           NOT NULL,
    `created_date`                  DATETIME         NOT NULL,
    `talent_id`                     BIGINT           NOT NULL,
    `tenant_id`                     BIGINT           NOT NULL,
    `node_id`                       BIGINT           NOT NULL,
    `note`                          VARCHAR(1048576) NULL,
    `node_type`                     INT              NOT NULL,
    `user_id`                       BIGINT           NOT NULL,
    `last_modified_by_user_id`      BIGINT           NULL,
    `last_modified_date`            DATETIME         NULL,
    `created_date_los_angeles`      DATE             NULL,
    `created_date_shanghai`         DATE             NULL,
    `created_date_new_york`         DATE             NULL
) PRIMARY KEY (`talent_recruitment_process_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`talent_recruitment_process_id`)
ORDER BY (`talent_recruitment_process_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`start_fte_rate`
(
    `start_id`              BIGINT         NOT NULL,
    `id`                    BIGINT         NOT NULL,
    `created_date`          DATETIME       NOT NULL DEFAULT "1970-01-01 00:00:00",
    `currency`              INT            NOT NULL,
    `rate_unit_type`        INT            NULL,
    `total_billable_amount` DECIMAL(20, 2) NULL,
    `fee_type`              INT            NULL,
    `fee_percentage`        DECIMAL(12, 4) NULL,
    `total_bill_amount`     DECIMAL(20, 2) NULL,
    `created_by`            VARCHAR(150)   NOT NULL,
    `last_modified_by`      VARCHAR(150)   NULL,
    `last_modified_date`    DATETIME       NULL,
    `puser_id`              BIGINT         NULL,
    `pteam_id`              BIGINT         NULL
) PRIMARY KEY (`start_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`start_id`)
ORDER BY (`start_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`company_additional_info`
(
    `company_id`    BIGINT           NOT NULL,
    `id`            BIGINT           NOT NULL,
    `extended_info` VARCHAR(1048576) NOT NULL
) PRIMARY KEY (`company_id`, `id`)
DISTRIBUTED BY HASH (`company_id`)
ORDER BY (`company_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`enum_work_authorization`
(
    `id`               INT          NOT NULL,
    `name`             VARCHAR(384) NOT NULL,
    `cn_display`       VARCHAR(384) NULL,
    `en_display`       VARCHAR(384) NULL,
    `en_display_order` INT          NULL,
    `cn_display_order` INT          NULL,
    `parent_category`  VARCHAR(384) NULL
) PRIMARY KEY (`id`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`company_project_team_user`
(
    `id`                 BIGINT       NOT NULL,
    `created_date`       DATETIME     NOT NULL DEFAULT "1970-01-01 00:00:00",
    `team_id`            BIGINT       NOT NULL,
    `user_id`            BIGINT       NOT NULL,
    `permission`         INT          NULL,
    `created_by`         VARCHAR(150) NOT NULL,
    `last_modified_by`   VARCHAR(150) NULL,
    `last_modified_date` DATETIME     NULL,
    `puser_id`           BIGINT       NULL,
    `pteam_id`           BIGINT       NULL
) PRIMARY KEY (`id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`assignment_contribution`
(
    `assignment_id` BIGINT         NOT NULL,
    `id`            BIGINT         NOT NULL,
    `user_id`       BIGINT         NULL,
    `user_role`     TINYINT        NULL,
    `percentage`    DECIMAL(10, 2) NULL
) PRIMARY KEY (`assignment_id`, `id`)
DISTRIBUTED BY HASH (`assignment_id`)
ORDER BY (`assignment_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`company_industry_relation`
(
    `company_id`  BIGINT NOT NULL,
    `id`          BIGINT NOT NULL,
    `industry_id` BIGINT NOT NULL
) PRIMARY KEY (`company_id`, `id`)
DISTRIBUTED BY HASH (`company_id`)
ORDER BY (`company_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`permission_user_team`
(
    `id`                 BIGINT       NOT NULL,
    `created_date`       DATETIME     NOT NULL DEFAULT "1970-01-01 00:00:00",
    `user_id`            BIGINT       NULL,
    `team_id`            BIGINT       NULL,
    `is_primary`         TINYINT      NULL,
    `tenant_id`          BIGINT       NULL,
    `created_by`         VARCHAR(150) NOT NULL,
    `last_modified_by`   VARCHAR(150) NULL,
    `last_modified_date` DATETIME     NULL
) PRIMARY KEY (`id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_recruitment_process_offer_fee_charge`
(
    `talent_recruitment_process_id` BIGINT         NOT NULL,
    `id`                            BIGINT         NOT NULL,
    `created_date`                  DATETIME       NOT NULL DEFAULT "1970-01-01 00:00:00",
    `total_billable_amount`         DECIMAL(20, 2) NULL,
    `fee_type`                      INT            NULL,
    `fee_amount`                    DECIMAL(12, 4) NULL,
    `total_amount`                  DECIMAL(20, 2) NULL,
    `created_by`                    VARCHAR(150)   NOT NULL,
    `last_modified_by`              VARCHAR(150)   NULL,
    `last_modified_date`            DATETIME       NULL,
    `puser_id`                      BIGINT         NULL,
    `pteam_id`                      BIGINT         NULL
) PRIMARY KEY (`talent_recruitment_process_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`talent_recruitment_process_id`)
ORDER BY (`talent_recruitment_process_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`time_sheet_holiday_record`
(
    `assignment_id`      BIGINT         NOT NULL,
    `id`                 BIGINT         NOT NULL,
    `created_date`       DATETIME       NOT NULL DEFAULT "1970-01-01 00:00:00",
    `work_hours`         DECIMAL(10, 2) NULL,
    `week_end`           DATETIME       NULL,
    `rate`               DECIMAL(10, 2) NULL,
    `currency`           TINYINT        NOT NULL,
    `created_by`         VARCHAR(150)   NOT NULL,
    `last_modified_by`   VARCHAR(150)   NULL,
    `last_modified_date` DATETIME       NULL
) PRIMARY KEY (`assignment_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`assignment_id`)
ORDER BY (`assignment_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`currency_rate_day`
(
    `id`                 BIGINT       NOT NULL,
    `created_date`       DATETIME     NOT NULL,
    `currency_id`        BIGINT       NULL,
    `currency_name`      VARCHAR(765) NULL,
    `rate_day`           DATE         NULL,
    `from_usd_rate_high` FLOAT        NULL,
    `from_usd_rate_mid`  FLOAT        NULL,
    `from_usd_rate_low`  FLOAT        NULL,
    `created_by`         VARCHAR(150) NULL,
    `last_modified_by`   VARCHAR(150) NULL,
    `last_modified_date` DATETIME     NULL
) PRIMARY KEY (`id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`job_user_relation`
(
    `job_id`  BIGINT  NOT NULL,
    `id`      BIGINT  NOT NULL,
    `user_id` BIGINT  NOT NULL,
    `role`    TINYINT NOT NULL
) PRIMARY KEY (`job_id`, `id`)
DISTRIBUTED BY HASH (`job_id`)
ORDER BY (`job_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`start_address`
(
    `start_id`             BIGINT           NOT NULL,
    `id`                   BIGINT           NOT NULL,
    `created_date`         DATETIME         NOT NULL DEFAULT "1970-01-01 00:00:00",
    `address`              VARCHAR(765)     NULL,
    `address_2`            VARCHAR(765)     NULL,
    `city`                 VARCHAR(765)     NULL,
    `city_id`              BIGINT           NULL,
    `province`             VARCHAR(765)     NULL,
    `country`              VARCHAR(765)     NULL,
    `zipcode`              VARCHAR(765)     NULL,
    `location`             VARCHAR(900)     NULL,
    `created_by`           VARCHAR(150)     NOT NULL,
    `last_modified_by`     VARCHAR(150)     NULL,
    `last_modified_date`   DATETIME         NULL,
    `puser_id`             BIGINT           NULL,
    `pteam_id`             BIGINT           NULL,
    `original_loc`         VARCHAR(1048576) NULL,
    `address_line`         VARCHAR(765)     NULL,
    `residential_location` BIGINT           NULL
) PRIMARY KEY (`start_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`start_id`)
ORDER BY (`start_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`time_sheet_week_ending_record`
(
    `assignment_id`          BIGINT           NOT NULL,
    `id`                     BIGINT           NOT NULL,
    `created_date`           DATETIME         NOT NULL DEFAULT "1970-01-01 00:00:00",
    `record_id`              BIGINT           NOT NULL,
    `tenant_id`              BIGINT           NOT NULL,
    `talent_id`              BIGINT           NOT NULL,
    `job_id`                 BIGINT           NOT NULL,
    `company_id`             BIGINT           NOT NULL,
    `manager`                VARCHAR(150)     NULL,
    `manager_id`             BIGINT           NULL,
    `am_approver`            VARCHAR(150)     NULL,
    `am_approver_id`         BIGINT           NULL,
    `primary_manager`        VARCHAR(150)     NULL,
    `primary_manager_id`     BIGINT           NULL,
    `am`                     VARCHAR(12000)   NULL,
    `am_ids`                 VARCHAR(765)     NULL,
    `instructions`           VARCHAR(1048576) NULL,
    `overtime_type`          TINYINT          NULL,
    `is_except`              BOOLEAN          NULL,
    `client_ids`             VARCHAR(765)     NULL,
    `allow_submit_timesheet` BOOLEAN          NULL,
    `job_title`              VARCHAR(765)     NULL,
    `company_name`           VARCHAR(765)     NULL,
    `full_name`              VARCHAR(765)     NULL,
    `calculate_type`         TINYINT          NULL     DEFAULT "0",
    `approved_date`          DATETIME         NULL,
    `start_date`             DATETIME         NULL,
    `end_date`               DATETIME         NULL,
    `assignment_status`      TINYINT          NULL,
    `work_date`              DATETIME         NULL,
    `week_start`             DATETIME         NULL,
    `week_end`               DATETIME         NULL,
    `week_ending_date`       DATETIME         NULL,
    `work_hours`             DECIMAL(10, 2)   NULL,
    `regular_hours`          DECIMAL(10, 2)   NULL,
    `over_time`              DECIMAL(10, 2)   NULL,
    `double_time`            DECIMAL(10, 2)   NULL,
    `total_hours`            DECIMAL(10, 2)   NULL,
    `submitted_date`         DATETIME         NULL,
    `status`                 TINYINT          NULL,
    `employment_category`    TINYINT          NULL,
    `billing_frequency`      TINYINT          NULL,
    `payment_frequency`      TINYINT          NULL,
    `assignment_division`    TINYINT          NULL,
    `time_sheet_type`        TINYINT          NULL,
    `created_by`             VARCHAR(150)     NOT NULL,
    `last_modified_by`       VARCHAR(150)     NULL,
    `last_modified_date`     DATETIME         NULL,
    `last_modified_name`     VARCHAR(765)     NULL,
    `manager_phone`          VARCHAR(765)     NULL,
    `primary_phone`          VARCHAR(765)     NULL
) PRIMARY KEY (`assignment_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`assignment_id`)
ORDER BY (`assignment_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_recruitment_process_kpi_user`
(
    `talent_recruitment_process_id` BIGINT         NOT NULL,
    `id`                            BIGINT         NOT NULL,
    `created_date`                  DATETIME       NOT NULL DEFAULT "1970-01-01 00:00:00",
    `user_id`                       BIGINT         NOT NULL,
    `user_role`                     INT            NULL,
    `percentage`                    DECIMAL(10, 4) NULL,
    `currency`                      INT            NULL,
    `amount`                        DECIMAL(10, 2) NULL,
    `created_by`                    VARCHAR(150)   NOT NULL,
    `last_modified_by`              VARCHAR(150)   NULL,
    `last_modified_date`            DATETIME       NULL,
    `puser_id`                      BIGINT         NULL,
    `pteam_id`                      BIGINT         NULL,
    `country`                       VARCHAR(192)   NULL
) PRIMARY KEY (`talent_recruitment_process_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`talent_recruitment_process_id`)
ORDER BY (`talent_recruitment_process_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_recruitment_process_onboard_date`
(
    `talent_recruitment_process_id` BIGINT       NOT NULL,
    `id`                            BIGINT       NOT NULL,
    `created_date`                  DATETIME     NOT NULL DEFAULT "1970-01-01 00:00:00",
    `onboard_date`                  DATE         NULL,
    `warranty_end_date`             DATE         NULL,
    `end_date`                      DATE         NULL,
    `currency`                      INT          NULL,
    `rate_unit_type`                INT          NULL,
    `created_by`                    VARCHAR(150) NULL,
    `last_modified_by`              VARCHAR(150) NULL,
    `last_modified_date`            DATETIME     NULL,
    `puser_id`                      BIGINT       NULL,
    `pteam_id`                      BIGINT       NULL
) PRIMARY KEY (`talent_recruitment_process_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`talent_recruitment_process_id`)
ORDER BY (`talent_recruitment_process_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`permission_extra_role_team`
(
    `id`                 BIGINT       NOT NULL,
    `created_date`       DATETIME     NOT NULL DEFAULT "1970-01-01 00:00:00",
    `role_id`            BIGINT       NULL,
    `module`             TINYINT      NULL     DEFAULT "1",
    `team_id`            BIGINT       NULL,
    `tenant_id`          BIGINT       NULL,
    `writable`           TINYINT      NULL,
    `created_by`         VARCHAR(150) NULL,
    `last_modified_by`   VARCHAR(150) NULL,
    `last_modified_date` DATETIME     NULL
) PRIMARY KEY (`id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`enum_company_contact_category`
(
    `id`               INT          NOT NULL,
    `name`             VARCHAR(384) NULL,
    `en_display`       VARCHAR(384) NULL,
    `cn_display`       VARCHAR(384) NULL,
    `en_display_order` INT          NULL,
    `cn_display_order` INT          NULL
) PRIMARY KEY (`id`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`assignment_bill_info`
(
    `assignment_id`              BIGINT         NOT NULL,
    `id`                         BIGINT         NOT NULL,
    `contact_id`                 BIGINT         NULL,
    `is_except`                  BOOLEAN        NULL,
    `overtime_type`              TINYINT        NULL,
    `group_invoice_type`         TINYINT        NULL,
    `group_invoice_content`      VARCHAR(765)   NULL,
    `group_invoice_content_type` TINYINT        NULL,
    `expense_invoice`            TINYINT        NULL,
    `discount_type`              TINYINT        NULL,
    `payment_terms`              DECIMAL(10, 2) NULL,
    `net_bill_Rate`              DECIMAL(10, 2) NULL,
    `net_overtime_rate`          DECIMAL(10, 2) NULL,
    `net_doubletime_rate`        DECIMAL(10, 2) NULL,
    `hourly_gm`                  DECIMAL(10, 2) NULL,
    `assignment_division`        TINYINT        NULL
) PRIMARY KEY (`assignment_id`, `id`)
DISTRIBUTED BY HASH (`assignment_id`)
ORDER BY (`assignment_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`start_rate_change`
(
    `start_id`                        BIGINT         NOT NULL,
    `id`                              BIGINT         NOT NULL,
    `created_date`                    DATETIME       NOT NULL DEFAULT "1970-01-01 00:00:00",
    `tenant_id`                       BIGINT         NOT NULL,
    `extend_start_id`                 BIGINT         NULL,
    `talent_id`                       BIGINT         NULL,
    `talent_name`                     VARCHAR(765)   NULL,
    `job_id`                          BIGINT         NULL,
    `job_title`                       VARCHAR(765)   NULL,
    `company_id`                      BIGINT         NULL,
    `company`                         VARCHAR(765)   NULL,
    `application_id`                  BIGINT         NULL,
    `client_contact_id`               BIGINT         NULL,
    `position_type`                   INT            NOT NULL,
    `currency`                        INT            NOT NULL,
    `time_zone`                       VARCHAR(765)   NULL,
    `rate_unit_type`                  INT            NULL,
    `start_date`                      DATE           NULL,
    `total_bill_amount`               DECIMAL(10, 2) NULL,
    `work_city`                       VARCHAR(150)   NULL,
    `work_province`                   VARCHAR(150)   NULL,
    `work_country`                    VARCHAR(150)   NULL,
    `end_date`                        DATE           NULL,
    `final_bill_rate`                 DECIMAL(10, 2) NULL,
    `final_pay_rate`                  DECIMAL(10, 2) NULL,
    `tax_burden_rate`                 BIGINT         NULL,
    `msp_rate`                        BIGINT         NULL,
    `immigration_cost`                BIGINT         NULL,
    `extra_cost`                      DECIMAL(10, 2) NULL,
    `estimated_working_hour_per_week` DECIMAL(10, 2) NULL,
    `entered_by`                      BIGINT         NULL,
    `note`                            VARCHAR(765)   NULL,
    `status`                          INT            NOT NULL,
    `start_type`                      INT            NOT NULL,
    `created_by`                      VARCHAR(150)   NOT NULL,
    `last_modified_by`                VARCHAR(150)   NULL,
    `last_modified_date`              DATETIME       NULL,
    `puser_id`                        BIGINT         NULL,
    `pteam_id`                        BIGINT         NULL
) PRIMARY KEY (`start_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`start_id`)
ORDER BY (`start_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`timesheet_expense_record`
(
    `assignment_id`      BIGINT         NOT NULL,
    `id`                 BIGINT         NOT NULL,
    `created_date`       DATETIME       NOT NULL DEFAULT "1970-01-01 00:00:00",
    `tenant_id`          BIGINT         NOT NULL,
    `talent_id`          BIGINT         NOT NULL,
    `work_date`          DATETIME       NULL,
    `week_start`         DATETIME       NULL,
    `week_end`           DATETIME       NULL,
    `week_ending_date`   DATETIME       NULL,
    `cost`               DECIMAL(10, 2) NULL     DEFAULT "0.00",
    `s3_key`             VARCHAR(765)   NULL,
    `week_day`           VARCHAR(765)   NULL,
    `line_index`         VARCHAR(765)   NULL,
    `expense_index`      INT            NULL     DEFAULT "0",
    `submitted_date`     DATETIME       NULL,
    `status`             TINYINT        NULL,
    `expense_type`       TINYINT        NULL,
    `created_by`         VARCHAR(150)   NOT NULL,
    `last_modified_by`   VARCHAR(150)   NULL,
    `last_modified_date` DATETIME       NULL
) PRIMARY KEY (`assignment_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`assignment_id`)
ORDER BY (`assignment_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_recruitment_process_stop_statistics`
(
    `talent_recruitment_process_id` BIGINT       NOT NULL,
    `id`                            BIGINT       NOT NULL,
    `created_date`                  DATETIME     NOT NULL,
    `talent_id`                     BIGINT       NULL,
    `last_execution_date`           DATETIME     NULL,
    `created_by`                    VARCHAR(150) NULL,
    `last_modified_by`              VARCHAR(150) NULL,
    `last_modified_date`            DATETIME     NULL
) PRIMARY KEY (`talent_recruitment_process_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`talent_recruitment_process_id`)
ORDER BY (`talent_recruitment_process_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`enum_contact_type`
(
    `id`                INT       NOT NULL,
    `name`              CHAR(192) NULL,
    `cn_display`        CHAR(192) NULL,
    `en_display`        CHAR(192) NULL,
    `cn_display_order`  INT       NULL,
    `en_display_order`  INT       NULL,
    `duplication_check` BOOLEAN   NULL DEFAULT "0",
    `check_group`       INT       NULL
) PRIMARY KEY (`id`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`invoice_payment_record`
(
    `invoice_id`                 BIGINT         NOT NULL,
    `id`                         BIGINT         NOT NULL,
    `created_date`               DATETIME       NOT NULL DEFAULT "1970-01-01 00:00:00",
    `payment_date`               DATETIME       NULL,
    `paid_amount`                DECIMAL(10, 2) NULL,
    `close_without_full_payment` INT            NULL,
    `payment_method`             INT            NULL,
    `note`                       VARCHAR(15000) NULL,
    `created_by`                 VARCHAR(150)   NULL,
    `last_modified_by`           VARCHAR(150)   NULL,
    `last_modified_date`         DATETIME       NULL,
    `puser_id`                   BIGINT         NULL,
    `pteam_id`                   BIGINT         NULL,
    `paid_startup_fee`           INT            NULL,
    `startup_fee_invoice_no`     VARCHAR(150)   NULL,
    `apply_credit`               DECIMAL(10, 0) NULL,
    `startup_fee_date`           DATETIME       NULL,
    `startup_fee_amount`         DECIMAL(10, 2) NULL,
    `currency`                   INT            NOT NULL,
    `exchange_rate`              VARCHAR(60)    NULL,
    `activated`                  BOOLEAN        NOT NULL
) PRIMARY KEY (`invoice_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`invoice_id`)
ORDER BY (`invoice_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`user_role`
(
    `user_id` BIGINT NOT NULL,
    `id`      INT    NOT NULL,
    `role_id` BIGINT NULL
) PRIMARY KEY (`user_id`, `id`)
DISTRIBUTED BY HASH (`user_id`)
ORDER BY (`user_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`assignment_timesheet`
(
    `assignment_id`          BIGINT           NOT NULL,
    `id`                     BIGINT           NOT NULL,
    `timesheet_type`         TINYINT          NOT NULL,
    `calculate_type`         TINYINT          NULL DEFAULT "0",
    `frequency`              TINYINT          NOT NULL,
    `week_ending`            TINYINT          NOT NULL,
    `allow_submit_Timesheet` BOOLEAN          NOT NULL,
    `allow_submit_expense`   BOOLEAN          NOT NULL,
    `instructions`           VARCHAR(1048576) NULL
) PRIMARY KEY (`assignment_id`, `id`)
DISTRIBUTED BY HASH (`assignment_id`)
ORDER BY (`assignment_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`role`
(
    `id`                                       BIGINT       NOT NULL,
    `name`                                     VARCHAR(150) NULL,
    `data_scope`                               TINYINT      NULL,
    `client_contact_data_scope`                TINYINT      NULL DEFAULT "1",
    `report_data_scope`                        TINYINT      NULL DEFAULT "1",
    `is_internal`                              TINYINT      NULL,
    `user_id`                                  BIGINT       NULL,
    `tenant_id`                                BIGINT       NULL,
    `status`                                   INT          NULL DEFAULT "0",
    `description`                              VARCHAR(300) NULL,
    `home_and_calendar_data_scope`             TINYINT      NULL DEFAULT "1",
    `candidate_pipeline_management_data_scope` TINYINT      NULL DEFAULT "1",
    `china_invoicing_data_scope`               TINYINT      NULL DEFAULT "1"
) PRIMARY KEY (`id`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`enum_invoicing_service_tax`
(
    `id`               BIGINT       NOT NULL,
    `service_name`     VARCHAR(765) NULL,
    `tax`              DOUBLE       NULL,
    `service_name_eng` VARCHAR(765) NULL
) PRIMARY KEY (`id`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`timesheet_breaktime_record`
(
    `assignment_id`      BIGINT       NOT NULL,
    `id`                 BIGINT       NOT NULL,
    `created_date`       DATETIME     NOT NULL DEFAULT "1970-01-01 00:00:00",
    `tenant_id`          BIGINT       NOT NULL,
    `talent_id`          BIGINT       NOT NULL,
    `work_date`          DATETIME     NULL,
    `time`               VARCHAR(765) NULL     DEFAULT "",
    `week_day`           VARCHAR(765) NULL,
    `line_index`         INT          NULL,
    `status`             TINYINT      NULL,
    `break_time_type`    TINYINT      NULL,
    `created_by`         VARCHAR(150) NOT NULL,
    `last_modified_by`   VARCHAR(150) NULL,
    `last_modified_date` DATETIME     NULL
) PRIMARY KEY (`assignment_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`assignment_id`)
ORDER BY (`assignment_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`job_company_contact_relation`
(
    `id`                 BIGINT       NOT NULL,
    `created_date`       DATETIME     NOT NULL DEFAULT "1970-01-01 00:00:00",
    `contact_category`   TINYINT      NOT NULL,
    `client_contact_id`  BIGINT       NOT NULL,
    `job_id`             BIGINT       NOT NULL,
    `talent_id`          BIGINT       NULL,
    `created_by`         VARCHAR(150) NOT NULL,
    `last_modified_by`   VARCHAR(150) NULL,
    `last_modified_date` DATETIME     NULL,
    `puser_id`           BIGINT       NULL,
    `pteam_id`           BIGINT       NULL
) PRIMARY KEY (`id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`invoice`
(
    `id`                     BIGINT         NOT NULL,
    `created_date`           DATETIME       NOT NULL DEFAULT "1970-01-01 00:00:00",
    `tenant_id`              BIGINT         NULL,
    `invoice_no`             VARCHAR(150)   NOT NULL,
    `sub_invoice_no`         VARCHAR(150)   NULL,
    `split`                  INT            NULL,
    `invoice_type`           INT            NULL,
    `status`                 INT            NULL,
    `start_id`               BIGINT         NULL,
    `start_date`             DATETIME       NULL,
    `job_id`                 BIGINT         NULL,
    `job_title`              VARCHAR(765)   NULL,
    `talent_id`              BIGINT         NULL,
    `talent_name`            VARCHAR(765)   NULL,
    `po_no`                  VARCHAR(300)   NULL,
    `customer_reference`     VARCHAR(300)   NULL,
    `company_id`             BIGINT         NULL,
    `customer_name`          VARCHAR(765)   NULL,
    `customer_address`       VARCHAR(765)   NULL,
    `client_contact_id`      BIGINT         NULL,
    `team_id`                BIGINT         NULL,
    `total_billable_package` DECIMAL(10, 2) NULL,
    `total_bill_amount`      DECIMAL(10, 2) NULL,
    `discount`               DECIMAL(10, 2) NULL,
    `total_invoice_amount`   DECIMAL(10, 2) NULL,
    `tax_rate`               DECIMAL(10, 4) NULL,
    `tax_amount`             DECIMAL(10, 2) NULL,
    `due_amount`             DECIMAL(12, 2) NULL,
    `receiving_account_id`   BIGINT         NULL,
    `invoice_date`           DATETIME       NULL,
    `due_date`               DATETIME       NULL,
    `currency`               INT            NOT NULL,
    `note`                   VARCHAR(15000) NULL,
    `created_by`             VARCHAR(150)   NULL,
    `last_modified_by`       VARCHAR(150)   NULL,
    `last_modified_date`     DATETIME       NULL,
    `puser_id`               BIGINT         NULL,
    `pteam_id`               BIGINT         NULL
) PRIMARY KEY (`id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_recruitment_process_offer`
(
    `talent_recruitment_process_id` BIGINT           NOT NULL,
    `id`                            BIGINT           NOT NULL,
    `created_date`                  DATETIME         NOT NULL DEFAULT "1970-01-01 00:00:00",
    `signed_date`                   DATE             NULL,
    `estimate_onboard_date`         DATE             NULL,
    `note`                          VARCHAR(1048576) NULL,
    `created_by`                    VARCHAR(150)     NOT NULL,
    `last_modified_by`              VARCHAR(150)     NULL,
    `last_modified_date`            DATETIME         NULL,
    `puser_id`                      BIGINT           NULL,
    `pteam_id`                      BIGINT           NULL,
    `note_last_modified_by_user_id` BIGINT           NULL,
    `note_last_modified_date`       DATETIME         NULL,
    `created_date_los_angeles`      DATE             NULL,
    `created_date_shanghai`         DATE             NULL,
    `created_date_new_york`         DATE             NULL,
    `last_update_user_id`           BIGINT           NULL
) PRIMARY KEY (`talent_recruitment_process_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`talent_recruitment_process_id`)
ORDER BY (`talent_recruitment_process_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`date_dimension`
(
    `date`            DATE        NOT NULL,
    `year`            INT         NULL,
    `month`           INT         NULL,
    `day`             INT         NULL,
    `day_of_week`     INT         NULL,
    `week_of_year`    INT         NULL,
    `start_of_week`   DATE        NULL,
    `end_of_week`     DATE        NULL,
    `quarter`         INT         NULL,
    `start_of_month`  DATE        NULL,
    `quarter_of_year` VARCHAR(18) NULL
) PRIMARY KEY (`date`)
DISTRIBUTED BY HASH (`date`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_language_relation`
(
    `id`                 BIGINT       NOT NULL,
    `created_date`       DATETIME     NOT NULL DEFAULT "1970-01-01 00:00:00",
    `talent_id`          BIGINT       NULL,
    `language_id`        INT          NOT NULL,
    `created_by`         VARCHAR(150) NOT NULL DEFAULT "",
    `last_modified_by`   VARCHAR(150) NULL,
    `last_modified_date` DATETIME     NULL
) PRIMARY KEY (`id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_recruitment_process_submit_to_job`
(
    `talent_recruitment_process_id` BIGINT           NOT NULL,
    `id`                            BIGINT           NOT NULL,
    `created_date`                  DATETIME         NOT NULL DEFAULT "1970-01-01 00:00:00",
    `note`                          VARCHAR(1048576) NULL,
    `skills`                        VARCHAR(1048576) NULL,
    `recommend_comments`            VARCHAR(1048576) NULL,
    `created_by`                    VARCHAR(150)     NOT NULL,
    `last_modified_by`              VARCHAR(150)     NULL,
    `last_modified_date`            DATETIME         NULL,
    `puser_id`                      BIGINT           NULL,
    `pteam_id`                      BIGINT           NULL,
    `talent_resume_relation_id`     BIGINT           NULL,
    `note_last_modified_by_user_id` BIGINT           NULL,
    `note_last_modified_date`       DATETIME         NULL,
    `created_date_los_angeles`      DATE             NULL,
    `created_date_shanghai`         DATE             NULL,
    `created_date_new_york`         DATE             NULL,
    `last_update_user_id`           BIGINT           NULL
) PRIMARY KEY (`talent_recruitment_process_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`talent_recruitment_process_id`)
ORDER BY (`talent_recruitment_process_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`enum_currency`
(
    `id`                    INT              NOT NULL,
    `name`                  CHAR(192)        NULL,
    `from_usd_rate`         FLOAT            NULL,
    `to_usd_rate`           FLOAT            NULL,
    `pattern`               VARCHAR(1048576) NULL,
    `cn_display`            CHAR(192)        NULL,
    `en_display`            CHAR(192)        NULL,
    `en_display_order`      INT              NULL,
    `cn_display_order`      INT              NULL,
    `symbol`                VARCHAR(765)     NULL,
    `code_symbol`           VARCHAR(765)     NULL,
    `country_code_symbol`   VARCHAR(765)     NULL,
    `last_rate_update_date` DATETIME         NULL
) PRIMARY KEY (`id`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`timesheet_calculate_state`
(
    `id`             BIGINT       NOT NULL,
    `is_default`     BOOLEAN      NOT NULL,
    `calculate_type` TINYINT      NOT NULL,
    `state`          VARCHAR(765) NOT NULL,
    `country_code`   VARCHAR(765) NOT NULL
) PRIMARY KEY (`id`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`enum_company_service_type`
(
    `id`               INT          NOT NULL,
    `name`             VARCHAR(384) NOT NULL,
    `en_display`       VARCHAR(384) NULL,
    `cn_display`       VARCHAR(384) NULL,
    `en_sort_order`    INT          NULL,
    `cn_sort_order`    INT          NULL,
    `en_display_order` INT          NULL,
    `cn_display_order` INT          NULL,
    `parent_category`  VARCHAR(384) NULL,
    `tier`             TINYINT      NOT NULL
) PRIMARY KEY (`id`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_recruitment_process_node`
(
    `talent_recruitment_process_id` BIGINT       NOT NULL,
    `id`                            BIGINT       NOT NULL,
    `created_date`                  DATETIME     NOT NULL DEFAULT "1970-01-01 00:00:00",
    `node_id`                       BIGINT       NOT NULL,
    `node_type`                     INT          NOT NULL,
    `node_status`                   INT          NOT NULL,
    `next_node_id`                  BIGINT       NULL,
    `created_by`                    VARCHAR(150) NOT NULL,
    `last_modified_by`              VARCHAR(150) NULL,
    `last_modified_date`            DATETIME     NULL,
    `puser_id`                      BIGINT       NULL,
    `pteam_id`                      BIGINT       NULL
) PRIMARY KEY (`talent_recruitment_process_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`talent_recruitment_process_id`)
ORDER BY (`talent_recruitment_process_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`start_client_info`
(
    `start_id`           BIGINT        NOT NULL,
    `id`                 BIGINT        NOT NULL,
    `created_date`       DATETIME      NOT NULL DEFAULT "1970-01-01 00:00:00",
    `client_name`        VARCHAR(375)  NULL,
    `client_division`    VARCHAR(150)  NULL,
    `client_address`     VARCHAR(765)  NULL,
    `client_email`       VARCHAR(300)  NULL,
    `created_by`         VARCHAR(150)  NOT NULL,
    `last_modified_by`   VARCHAR(150)  NULL,
    `last_modified_date` DATETIME      NULL,
    `puser_id`           BIGINT        NULL,
    `pteam_id`           BIGINT        NULL,
    `client_location`    VARCHAR(3072) NULL,
    `client_info_id`     BIGINT        NULL,
    `invoice_type_id`    BIGINT        NULL
) PRIMARY KEY (`start_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`start_id`)
ORDER BY (`start_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`t_group_invoice`
(
    `id`                   BIGINT         NOT NULL,
    `created_date`         DATETIME       NOT NULL DEFAULT "1970-01-01 00:00:00",
    `group_number`         VARCHAR(192)   NULL,
    `tenant_id`            BIGINT         NULL,
    `company_id`           BIGINT         NULL,
    `company_name`         VARCHAR(192)   NULL,
    `invoice_type`         TINYINT        NULL,
    `group_type`           TINYINT        NULL,
    `due_within_days`      INT            NULL,
    `payment_due_date`     DATETIME       NULL,
    `invoice_date`         DATETIME       NULL,
    `include_timesheet`    BOOLEAN        NULL,
    `group_invoice_status` TINYINT        NULL,
    `invoice_amount`       DECIMAL(10, 2) NULL,
    `amount_due`           DECIMAL(10, 2) NULL,
    `currency_id`          INT            NULL,
    `file_url`             VARCHAR(765)   NULL,
    `timesheet_file_url`   VARCHAR(765)   NULL,
    `assignment_division`  VARCHAR(765)   NULL,
    `operated_by_name`     VARCHAR(192)   NULL,
    `operated_by_id`       BIGINT         NULL,
    `operated_date`        DATETIME       NULL,
    `status`               BOOLEAN        NULL,
    `created_by`           VARCHAR(150)   NOT NULL,
    `last_modified_by`     VARCHAR(150)   NULL,
    `last_modified_date`   DATETIME       NULL
) PRIMARY KEY (`id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_recruitment_process_eliminate`
(
    `talent_recruitment_process_id` BIGINT           NOT NULL,
    `id`                            BIGINT           NOT NULL,
    `created_date`                  DATETIME         NOT NULL DEFAULT "1970-01-01 00:00:00",
    `reason`                        INT              NOT NULL,
    `note`                          VARCHAR(1048576) NULL,
    `created_by`                    VARCHAR(150)     NOT NULL,
    `last_modified_by`              VARCHAR(150)     NULL,
    `last_modified_date`            DATETIME         NULL,
    `puser_id`                      BIGINT           NULL,
    `pteam_id`                      BIGINT           NULL,
    `note_last_modified_by_user_id` BIGINT           NULL,
    `note_last_modified_date`       DATETIME         NULL,
    `created_date_los_angeles`      DATE             NULL,
    `created_date_shanghai`         DATE             NULL,
    `created_date_new_york`         DATE             NULL,
    `last_update_user_id`           BIGINT           NULL
) PRIMARY KEY (`talent_recruitment_process_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`talent_recruitment_process_id`)
ORDER BY (`talent_recruitment_process_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`job_location`
(
    `job_id`            BIGINT           NOT NULL,
    `id`                BIGINT           NOT NULL,
    `official_country`  VARCHAR(765)     NULL,
    `official_city`     VARCHAR(765)     NULL,
    `official_province` VARCHAR(765)     NULL,
    `official_county`   VARCHAR(765)     NULL,
    `original_loc`      VARCHAR(1048576) NULL,
    `format_location`   VARCHAR(3072)    NULL
) PRIMARY KEY (`job_id`, `id`)
DISTRIBUTED BY HASH (`job_id`)
ORDER BY (`job_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_recruitment_process_commission`
(
    `talent_recruitment_process_id` BIGINT           NOT NULL,
    `id`                            BIGINT           NOT NULL,
    `created_date`                  DATETIME         NOT NULL DEFAULT "1970-01-01 00:00:00",
    `note`                          VARCHAR(1048576) NULL,
    `created_by`                    VARCHAR(150)     NOT NULL,
    `last_modified_by`              VARCHAR(150)     NULL,
    `last_modified_date`            DATETIME         NULL,
    `puser_id`                      BIGINT           NULL,
    `pteam_id`                      BIGINT           NULL,
    `note_last_modified_by_user_id` BIGINT           NULL,
    `note_last_modified_date`       DATETIME         NULL
) PRIMARY KEY (`talent_recruitment_process_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`talent_recruitment_process_id`)
ORDER BY (`talent_recruitment_process_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`timesheet_talent_assignment`
(
    `id`                            BIGINT         NOT NULL,
    `created_date`                  DATETIME       NOT NULL DEFAULT "1970-01-01 00:00:00",
    `talent_id`                     BIGINT         NOT NULL,
    `tenant_id`                     BIGINT         NOT NULL,
    `talent_recruitment_process_id` BIGINT         NULL,
    `start_id`                      BIGINT         NOT NULL,
    `start_date`                    DATETIME       NULL,
    `end_date`                      DATETIME       NULL,
    `job_id`                        BIGINT         NULL,
    `company_id`                    BIGINT         NULL,
    `created_user_id`               BIGINT         NULL,
    `type`                          TINYINT        NULL,
    `status`                        TINYINT        NULL,
    `calculate_method`              TINYINT        NULL,
    `created_by`                    VARCHAR(150)   NOT NULL,
    `last_modified_by`              VARCHAR(150)   NULL,
    `last_modified_date`            DATETIME       NULL,
    `employment_category`           TINYINT        NULL,
    `working_hours`                 DECIMAL(20, 2) NULL,
    `is_week_end`                   BOOLEAN        NULL     DEFAULT "0"
) PRIMARY KEY (`id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_recruitment_process_ipg_fte_salary_package`
(
    `talent_recruitment_process_id` BIGINT         NOT NULL,
    `id`                            BIGINT         NOT NULL,
    `created_date`                  DATETIME       NOT NULL DEFAULT "1970-01-01 00:00:00",
    `salary_type`                   INT            NOT NULL,
    `amount`                        DECIMAL(10, 2) NOT NULL,
    `need_charge`                   BOOLEAN        NOT NULL,
    `created_by`                    VARCHAR(150)   NOT NULL,
    `last_modified_by`              VARCHAR(150)   NULL,
    `last_modified_date`            DATETIME       NULL,
    `puser_id`                      BIGINT         NULL,
    `pteam_id`                      BIGINT         NULL
) PRIMARY KEY (`talent_recruitment_process_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`talent_recruitment_process_id`)
ORDER BY (`talent_recruitment_process_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`company_user_relation`
(
    `company_id`          BIGINT  NOT NULL,
    `id`                  BIGINT  NOT NULL,
    `account_business_id` BIGINT  NULL,
    `project_id`          BIGINT  NULL,
    `user_id`             BIGINT  NOT NULL,
    `role`                TINYINT NOT NULL
) PRIMARY KEY (`company_id`, `id`)
DISTRIBUTED BY HASH (`company_id`)
ORDER BY (`company_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`user_account`
(
    `user_id`            BIGINT       NOT NULL,
    `id`                 BIGINT       NOT NULL,
    `created_date`       DATETIME     NOT NULL DEFAULT "1970-01-01 00:00:00",
    `account_type`       INT          NULL,
    `amount`             INT          NULL,
    `bulk_credit`        INT          NULL     DEFAULT "0",
    `frozen_amount`      INT          NULL,
    `total_amount`       INT          NULL,
    `account_status`     INT          NULL,
    `expire_date`        VARCHAR(150) NULL,
    `version`            INT          NULL     DEFAULT "0",
    `created_by`         VARCHAR(150) NULL,
    `last_modified_by`   VARCHAR(150) NULL,
    `last_modified_date` DATETIME     NULL,
    `credit_effect_type` TINYINT      NULL     DEFAULT "1",
    `effect_credit`      INT          NULL     DEFAULT "0"
) PRIMARY KEY (`user_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`user_id`)
ORDER BY (`user_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_recruitment_process_interview`
(
    `talent_recruitment_process_id` BIGINT           NOT NULL,
    `id`                            BIGINT           NOT NULL,
    `created_date`                  DATETIME         NOT NULL DEFAULT "1970-01-01 00:00:00",
    `progress`                      INT              NOT NULL,
    `from_time`                     DATETIME         NOT NULL DEFAULT "1970-01-01 00:00:00",
    `to_time`                       DATETIME         NOT NULL DEFAULT "1970-01-01 00:00:00",
    `interview_type`                INT              NULL,
    `time_zone`                     VARCHAR(150)     NULL,
    `note`                          VARCHAR(1048576) NULL,
    `created_by`                    VARCHAR(150)     NOT NULL,
    `last_modified_by`              VARCHAR(150)     NULL,
    `last_modified_date`            DATETIME         NULL,
    `puser_id`                      BIGINT           NULL,
    `pteam_id`                      BIGINT           NULL,
    `note_last_modified_by_user_id` BIGINT           NULL,
    `note_last_modified_date`       DATETIME         NULL,
    `created_date_los_angeles`      DATE             NULL,
    `created_date_shanghai`         DATE             NULL,
    `created_date_new_york`         DATE             NULL,
    `from_time_los_angeles`         DATE             NULL,
    `from_time_shanghai`            DATE             NULL,
    `from_time_new_york`            DATE             NULL,
    `from_time_utc`                 DATETIME         NULL,
    `final_round`                   TINYINT          NULL     DEFAULT "0",
    `last_update_user_id`           BIGINT           NULL
) PRIMARY KEY (`talent_recruitment_process_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`talent_recruitment_process_id`)
ORDER BY (`talent_recruitment_process_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_recruitment_process_onboard`
(
    `talent_recruitment_process_id` BIGINT           NOT NULL,
    `id`                            BIGINT           NOT NULL,
    `created_date`                  DATETIME         NOT NULL DEFAULT "1970-01-01 00:00:00",
    `note`                          VARCHAR(1048576) NULL,
    `created_by`                    VARCHAR(150)     NOT NULL,
    `last_modified_by`              VARCHAR(150)     NULL,
    `last_modified_date`            DATETIME         NULL,
    `puser_id`                      BIGINT           NULL,
    `pteam_id`                      BIGINT           NULL,
    `charge_number`                 VARCHAR(765)     NULL,
    `tvc_number`                    VARCHAR(765)     NULL,
    `corp_to_corp`                  BOOLEAN          NULL     DEFAULT "0",
    `note_last_modified_by_user_id` BIGINT           NULL,
    `note_last_modified_date`       DATETIME         NULL,
    `working_mode`                  INT              NULL,
    `created_date_los_angeles`      DATE             NULL,
    `created_date_shanghai`         DATE             NULL,
    `created_date_new_york`         DATE             NULL,
    `last_update_user_id`           BIGINT           NULL,
    `channel_platform`              INT              NULL,
    `profit_sharing_ratio`          DECIMAL(10, 4)   NULL,
    `is_substitute_talent`          TINYINT          NULL     DEFAULT "0",
    `relation_process_id`           BIGINT           NULL,
    `substitute_talent_id`          BIGINT           NULL
) PRIMARY KEY (`talent_recruitment_process_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`talent_recruitment_process_id`)
ORDER BY (`talent_recruitment_process_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`tenant`
(
    `id`                     BIGINT           NOT NULL,
    `created_date`           DATETIME         NOT NULL DEFAULT "1970-01-01 00:00:00",
    `name`                   VARCHAR(765)     NOT NULL,
    `company_id`             BIGINT           NULL,
    `created_by`             VARCHAR(150)     NOT NULL,
    `last_modified_by`       VARCHAR(150)     NULL,
    `last_modified_date`     DATETIME         NULL,
    `industry`               INT              NULL,
    `website`                VARCHAR(765)     NULL,
    `organization_name`      VARCHAR(300)     NULL,
    `staff_size`             TINYINT          NULL,
    `founded_date`           DATETIME         NULL,
    `address_id`             BIGINT           NULL,
    `description`            VARCHAR(1500)    NULL,
    `logo`                   VARCHAR(600)     NULL,
    `status`                 TINYINT          NULL     DEFAULT "1",
    `bulk_credit`            INT              NULL,
    `monthly_credit`         INT              NULL     DEFAULT "0",
    `update_monthly_credit`  INT              NULL     DEFAULT "0",
    `tenant_email`           VARCHAR(300)     NULL,
    `tenant_phone`           VARCHAR(300)     NULL,
    `expire_date`            DATETIME         NULL,
    `contact_name`           VARCHAR(765)     NULL,
    `contact_first_name`     VARCHAR(765)     NULL,
    `contact_last_name`      VARCHAR(765)     NULL,
    `user_max_limit`         INT              NULL,
    `note`                   VARCHAR(1048576) NULL,
    `login_link`             VARCHAR(765)     NULL,
    `user_type`              TINYINT          NULL,
    `reset_day_of_month`     TINYINT          NULL,
    `owner_data_restriction` TINYINT          NULL     DEFAULT "1"
) PRIMARY KEY (`id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`business_flow_administrator`
(
    `id`                  BIGINT       NOT NULL,
    `created_date`        DATETIME     NOT NULL DEFAULT "1970-01-01 00:00:00",
    `account_business_id` BIGINT       NULL,
    `user_id`             BIGINT       NOT NULL,
    `contribution`        INT          NULL,
    `sales_lead_role`     TINYINT      NULL,
    `company_id`          BIGINT       NULL,
    `created_by`          VARCHAR(150) NULL,
    `last_modified_by`    VARCHAR(150) NULL,
    `last_modified_date`  DATETIME     NULL,
    `puser_id`            BIGINT       NULL,
    `pteam_id`            BIGINT       NULL,
    `country`             BIGINT       NULL
) PRIMARY KEY (`id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`t_invoice_timesheet_info`
(
    `invoice_id`         BIGINT         NOT NULL,
    `id`                 BIGINT         NOT NULL,
    `created_date`       DATETIME       NOT NULL DEFAULT "1970-01-01 00:00:00",
    `currency_type`      INT            NULL,
    `quantity`           DECIMAL(10, 2) NULL,
    `quantity_type`      TINYINT        NULL,
    `item_description`   VARCHAR(1560)  NULL,
    `bill_rate`          DECIMAL(10, 2) NULL,
    `unit`               VARCHAR(30)    NULL,
    `total_amount`       DECIMAL(10, 2) NULL,
    `created_by`         VARCHAR(150)   NOT NULL,
    `last_modified_by`   VARCHAR(150)   NULL,
    `last_modified_date` DATETIME       NULL
) PRIMARY KEY (`invoice_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`invoice_id`)
ORDER BY (`invoice_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`user_crm_relation`
(
    `user_id`     BIGINT NOT NULL,
    `id`          BIGINT NOT NULL,
    `crm_user_id` BIGINT NOT NULL
) PRIMARY KEY (`user_id`, `id`)
DISTRIBUTED BY HASH (`user_id`)
ORDER BY (`user_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`enum_company_tag`
(
    `id`               BIGINT       NOT NULL,
    `name`             VARCHAR(384) NOT NULL,
    `en_display`       VARCHAR(384) NULL,
    `cn_display`       VARCHAR(384) NULL,
    `en_display_order` INT          NULL,
    `cn_display_order` INT          NULL
) PRIMARY KEY (`id`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_recruitment_process_onboard_work_location`
(
    `talent_recruitment_process_id` BIGINT           NOT NULL,
    `id`                            BIGINT           NOT NULL,
    `official_country`              VARCHAR(600)     NULL,
    `official_province`             VARCHAR(600)     NULL,
    `official_county`               VARCHAR(600)     NULL,
    `official_city`                 VARCHAR(600)     NULL,
    `original_loc`                  VARCHAR(1048576) NOT NULL,
    `address_line`                  VARCHAR(600)     NULL,
    `residential_location`          BIGINT           NULL
) PRIMARY KEY (`talent_recruitment_process_id`, `id`)
DISTRIBUTED BY HASH (`talent_recruitment_process_id`)
ORDER BY (`talent_recruitment_process_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_recruitment_process_submit_to_client`
(
    `talent_recruitment_process_id` BIGINT           NOT NULL,
    `id`                            BIGINT           NOT NULL,
    `created_date`                  DATETIME         NOT NULL DEFAULT "1970-01-01 00:00:00",
    `submit_time`                   DATETIME         NOT NULL DEFAULT "1970-01-01 00:00:00",
    `note`                          VARCHAR(1048576) NULL,
    `email_tracking_number`         VARCHAR(150)     NULL,
    `created_by`                    VARCHAR(150)     NOT NULL,
    `last_modified_by`              VARCHAR(150)     NULL,
    `last_modified_date`            DATETIME         NULL,
    `puser_id`                      BIGINT           NULL,
    `pteam_id`                      BIGINT           NULL,
    `note_last_modified_by_user_id` BIGINT           NULL,
    `note_last_modified_date`       DATETIME         NULL,
    `created_date_los_angeles`      DATE             NULL,
    `created_date_shanghai`         DATE             NULL,
    `created_date_new_york`         DATE             NULL,
    `submit_time_format`            DATE             NULL,
    `last_update_user_id`           BIGINT           NULL
) PRIMARY KEY (`talent_recruitment_process_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`talent_recruitment_process_id`)
ORDER BY (`talent_recruitment_process_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`company_project_team`
(
    `company_id`         BIGINT       NOT NULL,
    `id`                 BIGINT       NOT NULL,
    `created_date`       DATETIME     NOT NULL DEFAULT "1970-01-01 00:00:00",
    `tenant_id`          BIGINT       NOT NULL,
    `name`               VARCHAR(120) NOT NULL,
    `leader_user_id`     BIGINT       NULL,
    `created_by`         VARCHAR(150) NOT NULL,
    `last_modified_by`   VARCHAR(150) NULL,
    `last_modified_date` DATETIME     NULL,
    `puser_id`           BIGINT       NULL,
    `pteam_id`           BIGINT       NULL
) PRIMARY KEY (`company_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`company_id`)
ORDER BY (`company_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_recruitment_process_ipg_offer_letter_cost_rate`
(
    `id`                     BIGINT         NOT NULL,
    `tenant_id`              BIGINT         NOT NULL,
    `recruitment_process_id` BIGINT         NULL,
    `currency`               INT            NOT NULL,
    `rate_type`              INT            NOT NULL,
    `code`                   VARCHAR(300)   NOT NULL,
    `description`            VARCHAR(750)   NULL,
    `value`                  DECIMAL(10, 4) NOT NULL,
    `expire_date`            DATE           NOT NULL
) PRIMARY KEY (`id`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent`
(
    `id`                       BIGINT        NOT NULL,
    `created_date`             DATETIME      NOT NULL DEFAULT "1970-01-01 00:00:00",
    `tenant_id`                BIGINT        NOT NULL,
    `first_name`               VARCHAR(765)  NULL,
    `last_name`                VARCHAR(765)  NULL,
    `full_name`                VARCHAR(765)  NULL,
    `photo_url`                VARCHAR(3000) NULL,
    `birthday`                 VARCHAR(36)   NULL,
    `active`                   BOOLEAN       NULL,
    `created_by`               VARCHAR(150)  NOT NULL,
    `last_modified_by`         VARCHAR(150)  NULL,
    `last_modified_date`       DATETIME      NULL,
    `last_edited_time`         DATETIME      NULL,
    `last_sync_time`           DATETIME      NULL,
    `sync_paused`              INT           NOT NULL DEFAULT "0",
    `additional_info_id`       BIGINT        NULL,
    `puser_id`                 BIGINT        NULL,
    `pteam_id`                 BIGINT        NULL,
    `motivation_id`            INT           NULL,
    `owned_by_tenants`         BIGINT        NULL,
    `created_date_los_angeles` DATE          NULL,
    `created_date_shanghai`    DATE          NULL,
    `created_date_new_york`    DATE          NULL,
    `last_update_user_id`      BIGINT        NULL,
    `is_need_sync_hr`          BOOLEAN       NULL     DEFAULT "0"
) PRIMARY KEY (`id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`enum_motivation`
(
    `id`            INT       NOT NULL,
    `name`          CHAR(192) NULL,
    `cn_display`    CHAR(192) NULL,
    `en_display`    CHAR(192) NULL,
    `display_order` INT       NULL
) PRIMARY KEY (`id`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`permission_team`
(
    `id`                 BIGINT       NOT NULL,
    `created_date`       DATETIME     NOT NULL DEFAULT "1970-01-01 00:00:00",
    `name`               VARCHAR(150) NULL,
    `code`               VARCHAR(240) NULL,
    `parent_id`          BIGINT       NULL,
    `tenant_id`          BIGINT       NULL,
    `level`              INT          NULL,
    `is_leaf`            TINYINT      NULL,
    `deleted`            TINYINT      NULL,
    `created_by`         VARCHAR(150) NOT NULL,
    `last_modified_by`   VARCHAR(150) NULL,
    `last_modified_date` DATETIME     NULL
) PRIMARY KEY (`id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_recruitment_process_offer_salary`
(
    `talent_recruitment_process_id` BIGINT       NOT NULL,
    `id`                            BIGINT       NOT NULL,
    `created_date`                  DATETIME     NOT NULL DEFAULT "1970-01-01 00:00:00",
    `currency`                      INT          NOT NULL,
    `rate_unit_type`                INT          NOT NULL,
    `created_by`                    VARCHAR(150) NOT NULL,
    `last_modified_by`              VARCHAR(150) NULL,
    `last_modified_date`            DATETIME     NULL,
    `puser_id`                      BIGINT       NULL,
    `pteam_id`                      BIGINT       NULL
) PRIMARY KEY (`talent_recruitment_process_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`talent_recruitment_process_id`)
ORDER BY (`talent_recruitment_process_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`enum_business_progress`
(
    `id`               INT          NOT NULL,
    `name`             VARCHAR(384) NULL,
    `en_display`       VARCHAR(384) NULL,
    `cn_display`       VARCHAR(384) NULL,
    `en_sort_order`    INT          NULL,
    `cn_sort_order`    INT          NULL,
    `en_display_order` INT          NULL,
    `cn_display_order` INT          NULL
) PRIMARY KEY (`id`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`job_additional_info`
(
    `id`               BIGINT           NOT NULL,
    `extended_info`    VARCHAR(1048576) NULL,
    `department`       VARCHAR(765)     NULL,
    `puser_id`         BIGINT           NULL,
    `pteam_id`         BIGINT           NULL,
    `responsibilities` VARCHAR(1048576) NULL,
    `summary`          VARCHAR(1048576) NULL,
    `requirements`     VARCHAR(1048576) NULL,
    `public_desc`      VARCHAR(1048576) NULL
) PRIMARY KEY (`id`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`assignment_pay_info`
(
    `assignment_id`       BIGINT           NOT NULL,
    `id`                  BIGINT           NOT NULL,
    `is_except`           BOOLEAN          NULL,
    `employment_category` TINYINT          NULL,
    `comments`            VARCHAR(1048576) NULL,
    `pay_rate`            DECIMAL(10, 2)   NULL     DEFAULT "0.00",
    `frequency`           TINYINT          NOT NULL DEFAULT "1",
    `corporation`         VARCHAR(1048576) NULL
) PRIMARY KEY (`assignment_id`, `id`)
DISTRIBUTED BY HASH (`assignment_id`)
ORDER BY (`assignment_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`enum_job_priority`
(
    `id`         INT         NOT NULL,
    `level`      INT         NOT NULL,
    `name`       VARCHAR(30) NOT NULL,
    `en_display` VARCHAR(30) NOT NULL,
    `cn_display` VARCHAR(30) NULL
) PRIMARY KEY (`id`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`t_contractor_invoice`
(
    `id`                    BIGINT         NOT NULL,
    `created_date`          DATETIME       NOT NULL DEFAULT "1970-01-01 00:00:00",
    `invoice_number`        VARCHAR(660)   NULL,
    `group_invoice_number`  VARCHAR(660)   NULL,
    `p_o_number`            VARCHAR(1500)  NULL,
    `client_invoice_number` VARCHAR(1560)  NULL,
    `tenant_id`             BIGINT         NULL,
    `talent_id`             BIGINT         NULL,
    `talent_name`           VARCHAR(192)   NULL,
    `company_id`            BIGINT         NULL,
    `company_name`          VARCHAR(192)   NULL,
    `company_contact_id`    BIGINT         NULL,
    `company_contact_name`  VARCHAR(192)   NULL,
    `job_id`                BIGINT         NULL,
    `job_title`             VARCHAR(765)   NULL,
    `assignment_id`         BIGINT         NULL,
    `week_ending_date`      DATETIME       NULL,
    `file_url`              VARCHAR(765)   NULL,
    `invoice_type`          TINYINT        NULL,
    `invoice_date`          DATETIME       NULL,
    `invoice_status`        TINYINT        NULL,
    `assignment_division`   TINYINT        NULL,
    `note`                  VARCHAR(4800)  NULL,
    `comment_note`          VARCHAR(4500)  NULL,
    `total_amount`          DECIMAL(10, 2) NULL,
    `approver_id`           BIGINT         NULL,
    `approver_name`         VARCHAR(192)   NULL,
    `expense_comments`      VARCHAR(4500)  NULL,
    `expense_submit_date`   DATETIME       NULL,
    `approval_date`         DATETIME       NULL,
    `created_by`            VARCHAR(150)   NOT NULL,
    `last_modified_by`      VARCHAR(150)   NULL,
    `last_modified_date`    DATETIME       NULL,
    `client_location`       VARCHAR(3072)  NULL,
    `client_address`        VARCHAR(3072)  NULL,
    `client_name`           VARCHAR(3072)  NULL,
    `record_index`          INT            NULL     DEFAULT "0"
) PRIMARY KEY (`id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`time_sheet_user`
(
    `id`                 BIGINT       NOT NULL,
    `created_date`       DATETIME     NOT NULL DEFAULT "1970-01-01 00:00:00",
    `tenant_id`          BIGINT       NOT NULL,
    `uid`                VARCHAR(765) NOT NULL DEFAULT "",
    `user_name`          VARCHAR(150) NOT NULL,
    `password`           VARCHAR(240) NOT NULL,
    `email`              VARCHAR(300) NOT NULL,
    `is_pass_changed`    BOOLEAN      NULL,
    `user_type`          TINYINT      NULL,
    `time_sheet_type`    TINYINT      NULL,
    `activated`          BOOLEAN      NOT NULL DEFAULT "1",
    `created_by`         VARCHAR(150) NOT NULL,
    `last_modified_by`   VARCHAR(150) NULL,
    `last_modified_date` DATETIME     NULL,
    `first_name`         VARCHAR(150) NULL,
    `last_name`          VARCHAR(150) NULL,
    `uid_prefix`         BIGINT       NULL
) PRIMARY KEY (`id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`t_group_invoice_record`
(
    `invoice_id`            BIGINT       NOT NULL,
    `id`                    BIGINT       NOT NULL,
    `created_date`          DATETIME     NOT NULL DEFAULT "1970-01-01 00:00:00",
    `group_invoice_id`      BIGINT       NULL,
    `talent_id`             BIGINT       NULL,
    `internal_invoice_type` TINYINT      NULL,
    `status`                TINYINT      NULL,
    `created_by`            VARCHAR(150) NOT NULL,
    `last_modified_by`      VARCHAR(150) NULL,
    `last_modified_date`    DATETIME     NULL
) PRIMARY KEY (`invoice_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`invoice_id`)
ORDER BY (`invoice_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`t_record_payment_info`
(
    `group_invoice_id`     BIGINT         NOT NULL,
    `id`                   BIGINT         NOT NULL,
    `created_date`         DATETIME       NOT NULL DEFAULT "1970-01-01 00:00:00",
    `group_invoice_number` VARCHAR(192)   NULL,
    `payment_date`         DATETIME       NULL,
    `payment_amount`       DECIMAL(10, 2) NULL,
    `payment_method`       INT            NULL,
    `note`                 VARCHAR(6600)  NULL,
    `status`               BOOLEAN        NULL,
    `created_by`           VARCHAR(150)   NOT NULL,
    `last_modified_by`     VARCHAR(150)   NULL,
    `last_modified_date`   DATETIME       NULL,
    `purchase_order_id`    BIGINT         NULL
) PRIMARY KEY (`group_invoice_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`group_invoice_id`)
ORDER BY (`group_invoice_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_recruitment_process_offer_salary_package`
(
    `talent_recruitment_process_id` BIGINT         NOT NULL,
    `id`                            BIGINT         NOT NULL,
    `created_date`                  DATETIME       NOT NULL DEFAULT "1970-01-01 00:00:00",
    `salary_type`                   INT            NULL,
    `amount`                        DECIMAL(20, 2) NULL,
    `need_charge`                   BOOLEAN        NOT NULL,
    `created_by`                    VARCHAR(150)   NOT NULL,
    `last_modified_by`              VARCHAR(150)   NULL,
    `last_modified_date`            DATETIME       NULL,
    `puser_id`                      BIGINT         NULL,
    `pteam_id`                      BIGINT         NULL
) PRIMARY KEY (`talent_recruitment_process_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`talent_recruitment_process_id`)
ORDER BY (`talent_recruitment_process_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`start_commission`
(
    `start_id`           BIGINT         NOT NULL,
    `id`                 BIGINT         NOT NULL,
    `created_date`       DATETIME       NOT NULL DEFAULT "1970-01-01 00:00:00",
    `tenant_id`          BIGINT         NULL,
    `user_id`            BIGINT         NOT NULL,
    `user_full_name`     VARCHAR(450)   NULL,
    `user_role`          INT            NOT NULL,
    `percentage`         DECIMAL(10, 4) NULL,
    `period_start_date`  DATETIME       NULL,
    `period_end_date`    DATETIME       NULL,
    `created_by`         VARCHAR(150)   NOT NULL,
    `last_modified_by`   VARCHAR(150)   NULL,
    `last_modified_date` DATETIME       NULL,
    `puser_id`           BIGINT         NULL,
    `pteam_id`           BIGINT         NULL,
    `country`            VARCHAR(192)   NULL
) PRIMARY KEY (`start_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`start_id`)
ORDER BY (`start_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_additional_info`
(
    `id`                 BIGINT           NOT NULL,
    `created_date`       DATETIME         NOT NULL DEFAULT "1970-01-01 00:00:00",
    `extended_info`      VARCHAR(1048576) NULL,
    `created_by`         VARCHAR(150)     NOT NULL DEFAULT "",
    `last_modified_by`   VARCHAR(150)     NULL,
    `last_modified_date` DATETIME         NULL,
    `current_company`    VARCHAR(765)     NULL,
    `current_position`   VARCHAR(6144)    NULL,
    `source`             VARCHAR(765)     NULL
) PRIMARY KEY (`id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_recruitment_process_onboard_client_info`
(
    `talent_recruitment_process_id` BIGINT        NOT NULL,
    `id`                            BIGINT        NOT NULL,
    `created_date`                  DATETIME      NOT NULL DEFAULT "1970-01-01 00:00:00",
    `client_contact_id`             BIGINT        NULL,
    `client_name`                   VARCHAR(375)  NULL,
    `client_division`               VARCHAR(150)  NULL,
    `client_address`                VARCHAR(765)  NULL,
    `client_email`                  VARCHAR(300)  NULL,
    `created_by`                    VARCHAR(150)  NOT NULL,
    `last_modified_by`              VARCHAR(150)  NULL,
    `last_modified_date`            DATETIME      NULL,
    `puser_id`                      BIGINT        NULL,
    `pteam_id`                      BIGINT        NULL,
    `client_location`               VARCHAR(3072) NULL,
    `client_info_id`                BIGINT        NULL,
    `invoice_type_id`               BIGINT        NULL
) PRIMARY KEY (`talent_recruitment_process_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`talent_recruitment_process_id`)
ORDER BY (`talent_recruitment_process_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`tenant_address`
(
    `id`        BIGINT       NOT NULL,
    `address`   VARCHAR(765) NULL,
    `address_2` VARCHAR(765) NULL,
    `city_id`   BIGINT       NULL,
    `zipcode`   VARCHAR(765) NULL
) PRIMARY KEY (`id`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`enum_follow_up_contact_type`
(
    `id`               INT       NOT NULL,
    `name`             CHAR(192) NULL,
    `cn_display`       CHAR(192) NULL,
    `en_display`       CHAR(192) NULL,
    `cn_display_order` INT       NULL,
    `en_display_order` INT       NULL
) PRIMARY KEY (`id`)
DISTRIBUTED BY HASH (`id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`talent_resume_relation`
(
    `talent_id`          BIGINT       NOT NULL,
    `id`                 BIGINT       NOT NULL,
    `created_date`       DATETIME     NOT NULL DEFAULT "1970-01-01 00:00:00",
    `resume_id`          BIGINT       NOT NULL,
    `tenant_id`          BIGINT       NOT NULL,
    `file_name`          VARCHAR(765) NULL,
    `source_type`        TINYINT      NULL,
    `status`             TINYINT      NULL     DEFAULT "0",
    `created_by`         VARCHAR(150) NOT NULL,
    `last_modified_by`   VARCHAR(150) NULL,
    `last_modified_date` DATETIME     NULL,
    `puser_id`           BIGINT       NULL,
    `pteam_id`           BIGINT       NULL
) PRIMARY KEY (`talent_id`, `id`, `created_date`)
PARTITION BY date_trunc('month', `created_date`)
DISTRIBUTED BY HASH (`talent_id`)
ORDER BY (`talent_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);
CREATE TABLE IF NOT EXISTS `ods_apn`.`timesheet_comments`
(
    `assignment_id` BIGINT         NOT NULL,
    `id`            BIGINT         NOT NULL,
    `tenant_id`     BIGINT         NOT NULL,
    `talent_id`     BIGINT         NOT NULL,
    `comments`      VARCHAR(15000) NULL,
    `work_date`     DATETIME       NULL,
    `comments_type` TINYINT        NULL,
    `record_index`  INT            NULL DEFAULT "0"
) PRIMARY KEY (`assignment_id`, `id`)
DISTRIBUTED BY HASH (`assignment_id`)
ORDER BY (`assignment_id`)

PROPERTIES (
"enable_persistent_index" = "true",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "1",
"compression" = "LZ4"
);

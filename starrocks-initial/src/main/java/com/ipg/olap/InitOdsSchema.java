package com.ipg.olap;

import com.google.common.collect.ImmutableMap;
import com.ipg.olap.ods.OdsMetaData;
import com.ipg.olap.scmema.StarRocksUtils;
import com.ipg.olap.ods.customizer.TableCustomizer;
import com.ipg.olap.scmema.MysqlSchemaGetter;
import com.ipg.olap.scmema.StarRocksCatalog;
import com.ipg.olap.scmema.StarRocksTable;
import io.debezium.jdbc.JdbcConnection;
import org.apache.flink.cdc.common.event.TableId;
import org.apache.flink.cdc.connectors.mysql.source.config.MySqlSourceConfig;
import org.apache.flink.cdc.connectors.mysql.source.config.MySqlSourceConfigFactory;
import org.apache.flink.cdc.connectors.mysql.utils.MySqlSchemaUtils;
import org.apache.flink.cdc.connectors.starrocks.sink.TableCreateConfig;
import org.slf4j.Logger;
import org.apache.flink.cdc.common.schema.Schema;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.apache.flink.cdc.connectors.mysql.debezium.DebeziumUtils.openJdbcConnection;

public class InitOdsSchema {

    private static final Logger LOG = LoggerFactory.getLogger(InitOdsSchema.class);

    public static void main(String[] args) {
        String databaseName = OdsMetaData.sourceDataBaseName;
        List<String> tableList = OdsMetaData.ODS_TABLES;

        String mysqlHost = Env.mysqlHost();
        int mysqlPort = Env.mysqlPort();
        String mysqlUsername = Env.mysqlUser();
        String mysqlPassword = Env.mysqlPassword();

        String starRocksJdbcUrl = Env.starRocksJdbcUrl();
        String starRocksUsername = Env.starRocksUser();
        String starRocksPassword = Env.starRocksPassword();
        Integer beNum = Env.starrocksBeNums();
        String starrocksOdsDatabase = OdsMetaData.odsDataBaseName;
        String cdcTableList = tableList.stream().map(tName -> databaseName + "." + tName).collect(Collectors.joining(","));
        LOG.info("CDC table list: {}", cdcTableList);
        MySqlSourceConfig mySqlSourceConfig = new MySqlSourceConfigFactory()
            .hostname(mysqlHost)
            .port(mysqlPort)
            .databaseList(databaseName)
            .tableList(cdcTableList)
            .username(mysqlUsername)
            .password(mysqlPassword)
            .serverTimeZone("UTC")
            .serverId("5410-5450")
            .createConfig(1);

        StarRocksCatalog starrRocksCatalog = new StarRocksCatalog(starRocksJdbcUrl, starRocksUsername, starRocksPassword);
        starrRocksCatalog.createDatabase(starrocksOdsDatabase, true);

        List<TableId> tableIds = MySqlSchemaUtils.listTables(mySqlSourceConfig, databaseName);
        List<TableId> syncTableIds = tableIds.stream()
            .filter(tableId -> tableList.contains(tableId.getTableName())).collect(Collectors.toList());

        MysqlSchemaGetter mysqlSchemaGetter = new MysqlSchemaGetter();
        Map<TableId, Schema> schemaMap = syncTableIds.stream()
            .collect(Collectors.toMap(tableId -> TableId.tableId(starrocksOdsDatabase, tableId.getTableName()),
                tableId -> {
                    LOG.info("Getting schema for table {}.{}", tableId.getSchemaName(), tableId.getTableName());
                    io.debezium.relational.TableId tid = io.debezium.relational.TableId.parse(tableId.toString());
                    try (JdbcConnection jdbc = openJdbcConnection(mySqlSourceConfig)) {
                        return mysqlSchemaGetter.getSchema(jdbc, tid);
                    } catch (Exception e) {
                        LOG.error("Failed to get schema for table {}.{}", tableId.getSchemaName(), tableId.getTableName(), e);
                        throw new RuntimeException(e);
                    }
                }));
        TableCreateConfig tableCreateConfig = new TableCreateConfig(null, ImmutableMap.of(
            "compression", "LZ4",
            "enable_persistent_index", "true",
            "fast_schema_evolution", "true",
            "replicated_storage", "true",
            "replication_num", beNum + ""
        ));

        List<String> createTableSqls = new ArrayList<>();
        List<String> failedSqls = new ArrayList<>();
        List<String> failedTables = new ArrayList<>();
        schemaMap.forEach((tableId, schema) -> {

            StarRocksTable starRocksTable = StarRocksUtils.toStarRocksTable(tableId, schema, tableCreateConfig);
            StarRocksTable table = TableCustomizer.doCustomize(starRocksTable);
            String DDL = starrRocksCatalog.buildCreateTableSql(table, true);
            createTableSqls.add(DDL);
            try {
                starrRocksCatalog.createTable(table, true);
            } catch (Exception e) {
                LOG.error("Failed to create table {}.{}", tableId.getSchemaName(), tableId.getTableName(), e);
                failedSqls.add(DDL);
                failedTables.add(tableId.getTableName());
            }
        });

        LOG.info("Create table SQLs: \n{}", String.join("\n", createTableSqls));
        LOG.info("Failed tables: \n{}", String.join("\n", failedTables));
        LOG.info("Failed SQLs: \n{}", String.join("\n", failedSqls));
    }


}

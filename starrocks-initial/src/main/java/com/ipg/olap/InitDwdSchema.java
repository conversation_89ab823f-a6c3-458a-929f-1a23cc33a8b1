package com.ipg.olap;

import com.ipg.olap.dwd.DwdMetaData;
import com.ipg.olap.dwd.DwdTable;
import com.ipg.olap.dwd.customizer.DwdTableCustomizer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.List;

public class InitDwdSchema {

    private static final Logger log = LoggerFactory.getLogger(InitDwdSchema.class);

    public static void main(String[] args) throws SQLException {
        List<DwdTable> tableList = DwdMetaData.DWD_TABLES;

        String starRocksJdbcUrl = Env.starRocksJdbcUrl();
        String starRocksUsername = Env.starRocksUser();
        String starRocksPassword = Env.starRocksPassword();

        try (Connection connection = DriverManager.getConnection(starRocksJdbcUrl, starRocksUsername, starRocksPassword)) {
            Statement statement = connection.createStatement();
            statement.execute("CREATE DATABASE IF NOT EXISTS " + DwdMetaData.DWD_DATABASE);
        } catch (Exception e) {
           log.error("Failed to create database");
        }

        try (Connection connection = DriverManager.getConnection(starRocksJdbcUrl, starRocksUsername, starRocksPassword)) {
            Statement statement = connection.createStatement();
            statement.execute("USE " + DwdMetaData.DWD_DATABASE);
            for (DwdTable table : tableList) {
                DwdTableCustomizer customizer = table.getCustomizer().get();
                String DDL = customizer.customize(table);
                log.info("Creating table: {}", DDL);
                statement.execute(DDL);
            }
        } catch (Exception e) {
            log.error("Failed to create table", e);
        }

    }


}

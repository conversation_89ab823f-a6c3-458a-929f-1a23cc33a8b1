apiVersion: v1
data:
  cdc-config.yaml : |-
    paimon:
        type: "paimon"
        warehouse: "s3://olap-paimon-storage-staging/warehouse"
        metastore: "filesystem"
        s3.endpoint: "https://s3-ap-southeast-1.amazonaws.com"
        s3.access-key: "********************"
        s3.secret-key: "/8vRVAa/Sh8/LGlMlF7OuAx7A5V9dPyeSXvlkoaq"
    mysql:
        hostname: "apnv3-db.default"
        port: "3306"
        username: "apn_admin"
        password: "L19$a2?FRy-4"
        database-name: "apnv3"
        server-id: "5600-5610"
        server-time-zone: "UTC"
        scan.incremental.snapshot.enabled: "true"
        schema-change.enabled: "true"
        connection.pool.size: "10"
        connect.timeout: "60s"
        connect.max-retries: "3"
    paimon_table:
        changelog-producer: "input"
        sink.parallelism: "4"
        bucket: "4"
        write-buffer-size: "128mb"
        auto-compaction: "true"
        sink.buffer-flush.max-rows: "1000"
        sink.buffer-flush.interval: "30s"
        compaction.min.file-num: "3"
        compaction.max.file-num: "10"
        snapshot.time-retained: "7d"
        snapshot.num-retained.min: "3"
        snapshot.num-retained.max: "10"
    action:
        parallelism: "4"
        checkpoint.interval: "60000"
        target-database: "ods_apn"
        include-tables: ".*"
        primary-key: "id"
kind: ConfigMap
metadata:
  name: olap-apn-to-paimon-configmap
# kubectl delete flinkdeployment mysql-to-starrocks-cdc-pipeline-job -n olap --force --grace-period=0
apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  namespace: olap
  name: olap-apn-to-paimon-cdc-action-job
spec:
  flinkConfiguration:
    classloader.resolve-order: parent-first
    execution.checkpointing.dir: "file:///opt/flink/data/checkpoints"
    execution.checkpointing.savepoint-dir: "file:///opt/flink/data/savepoints"
    execution.checkpointing.timeout: 1200s
    execution.checkpointing.max-concurrent-checkpoints: "1"
    taskmanager.memory.managed.size: "512m"
    taskmanager.numberOfTaskSlots: "4"
    pekko.ask.timeout: 180s
    state.backend.type: "rocksdb"

    kubernetes.operator.savepoint.history.max.age: 7d
    kubernetes.operator.savepoint.history.max.count: "3"
    kubernetes.operator.periodic.savepoint.interval: 6h
    kubernetes.operator.periodic.checkpoint.interval: 60s
    kubernetes.operator.savepoint.cleanup.enabled: "true"
    kubernetes.operator.savepoint.dispose-on-delete: "true"
    kubernetes.operator.snapshot.resource.timeout: 10m
    kubernetes.operator.observer.rest-ready.delay: 10s
    kubernetes.operator.reconcile.interval: 30s
    kubernetes.operator.job.restart.failed: "true"

  flinkVersion: v1_20
  image: "minghealtomni/flink_mysql_paimon:**************"
  imagePullPolicy: IfNotPresent
  restartNonce: 1
  serviceAccount: flink
  job:
    args:
      - /opt/flink/cdc-conf/cdc-config.yaml
    entryClass: com.ipg.olap.stream.job.PaimonOdsSyncJob
    jarURI: 'local:///opt/flink/app.jar'
    parallelism: 1
    state: running
    upgradeMode: savepoint
  jobManager:
    replicas: 1
    resource:
      cpu: 0.25
      memory: 1024m
  taskManager:
    resource:
      cpu: 1
      memory: 4096m
  podTemplate:
    apiVersion: v1
    kind: Pod
    spec:
      nodeSelector:
        role: "olap-node"
      affinity:
        podAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchLabels:
                component: jobmanager
                app: olap-apn-to-paimon-cdc-action-job
            topologyKey: kubernetes.io/hostname
      # 使用 securityContext 替代 initContainers
      securityContext:
        fsGroup: 9999  # flink 用户的组 ID
        runAsUser: 9999  # flink 用户的 ID
      imagePullSecrets:
      - name: altomni-docker
      containers:
        # don't modify this name
        - name: flink-main-container
          envFrom:
            - configMapRef:
                name: olap-apn-to-paimon-configmap
          volumeMounts:
            - mountPath: /opt/flink/cdc-conf
              name: apn-to-paimon-config
            - mountPath: /opt/flink/data
              name: flink-state-volume
      volumes:
        - name: apn-to-paimon-config
          configMap:
            name: olap-apn-to-paimon-configmap
        - name: flink-state-volume
          persistentVolumeClaim:
            claimName: olap-apn-flink-paimon-state-pvc

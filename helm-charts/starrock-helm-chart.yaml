operator:
  global:
    rbac:
      create: true
      serviceAccount:
        annotations: {}
        labels: {}
        name: starrocks
  nameOverride: kube-starrocks
  starrocksOperator:
    annotations: {}
    enabled: true
    env: []
    image:
      repository: starrocks/operator
      tag: v1.10.2
    imagePullPolicy: Always
    namespaceOverride: ""
    replicaCount: 1
    resources:
      limits:
        cpu: 500m
        memory: 800Mi
      requests:
        cpu: 500m
        memory: 400Mi
    tolerations: []
    watchNamespace: ""
  timeZone: UTC
starrocks:
  configMaps: []
  datadog:
    log:
      enabled: false
      logConfig: "{}"
    metrics:
      enabled: false
  initPassword:
    enabled: true
    password: 'xw7#]!ZQ"nDnKa'
    passwordSecret: ""
  metrics:
    serviceMonitor:
      enabled: false
      interval: 15s
  nameOverride: kube-starrocks
  secrets: []
  starrocksBeSpec:
    affinity: {}
    annotations: {}
    beEnvVars: []
    config: |
      be_port = 9060
      webserver_port = 8040
      heartbeat_service_port = 9050
      brpc_port = 8060
      default_rowset_type = beta

      sys_log_level = INFO

      sys_log_roll_mode = SIZE-MB-512
      
      sys_log_roll_num = 1

      enable_spill = true
      storage_root_path = /opt/starrocks/be/storage
    configMaps: []
    hostAliases: []
    image:
      repository: starrocks/be-ubuntu
      tag: 3.4-latest
    imagePullSecrets: []
    livenessProbeFailureSeconds: null
    affinity:
      podAffinity: {}
      podAntiAffinity:
        requiredDuringSchedulingIgnoredDuringExecution:
        - labelSelector:
            matchExpressions:
            - key: starrocks
              operator: In
              values:
              - be
          topologyKey: kubernetes.io/hostname
    podLabels:
      starrocks: be
    readinessProbeFailureSeconds: null
    replicas: 2
    resources:
      limits:
        cpu: 4
        memory: 8Gi
      requests:
        cpu: 200m
        memory: 1Gi
    runAsNonRoot: false
    schedulerName: ""
    secrets: []
    service:
      annotations: {}
      loadbalancerIP: ""
      ports: []
      type: ClusterIP
    serviceAccount: ""
    startupProbeFailureSeconds: null
    storageSpec:
      logStorageSize: "2Gi"
      name: "starrocks-be-vol"
      storageClassName: "manualcleanup-gp3"
      storageSize: "25Gi"
    terminationGracePeriodSeconds: 120
    tolerations: []
  starrocksCluster:
    annotations: {}
    enabledCn: false
    name: "starrocks"
    namespace: "olap"
  starrocksFESpec:
    affinity: {}
    annotations: {}
    config: >
      LOG_DIR = ${STARROCKS_HOME}/log

      DATE = "$(date +%Y%m%d-%H%M%S)"

      JAVA_OPTS="-Dlog4j2.formatMsgNoLookups=true -Xmx2048m
      -XX:+UseG1GC -Xlog:gc=info,heap*=debug:file=${LOG_DIR}/fe.gc.log.$DATE:time,uptime,level,tags:filecount=5,filesize=10M"

      http_port = 8030

      rpc_port = 9020

      query_port = 9030

      edit_log_port = 9010

      mysql_service_nio_enabled = true

      enable_query_cache = true

      sys_log_level = INFO

      log_roll_size_mb = 512

      sys_log_roll_num = 1

      sys_log_delete_age = 3d

      audit_log_roll_num = 2

      audit_log_delete_age = 7d
    configMaps: []
    feEnvVars: []
    hostAliases: []
    image:
      repository: starrocks/fe-ubuntu
      tag: 3.4-latest
    imagePullSecrets: []
    livenessProbeFailureSeconds: null
    affinity:
      podAffinity: {}
      podAntiAffinity:
        requiredDuringSchedulingIgnoredDuringExecution:
        - labelSelector:
            matchExpressions:
            - key: starrocks-fe
              operator: In
              values:
              - fe
          topologyKey: kubernetes.io/hostname
    podLabels:
      starrocks: fe
    readinessProbeFailureSeconds: null
    replicas: 1
    resources:
      limits:
        cpu: 4
        memory: 4Gi
      requests:
        cpu: 200m
        memory: 1Gi
    runAsNonRoot: false
    schedulerName: ""
    secrets: []
    service:
      annotations: {}
      loadbalancerIP: ""
      ports: []
      type: LoadBalancer
    serviceAccount: ""
    startupProbeFailureSeconds: null
    storageSpec:
      logStorageSize: "2Gi"
      name: "starrocks-fe-vol"
      storageClassName: "manualcleanup-gp3"
      storageSize: "10Gi"
    terminationGracePeriodSeconds: 120
    tolerations: []
  starrocksFeProxySpec:
    affinity: {}
    enabled: false
    image:
      repository: ""
      tag: ""
    livenessProbeFailureSeconds: null
    nodeSelector:
      role: "olap-node"
    readinessProbeFailureSeconds: null
    replicas: 1
    resolver: ""
    resources:
      limits:
        cpu: 1
        memory: 1Gi
      requests:
        cpu: 100m
        memory: 100Mi
    service:
      loadbalancerIP: ""
      ports: []
      type: LoadBalancer
    tolerations: []
  timeZone: UTC

apiVersion: v1
data:
  flink-cdc.yaml: |-
      parallelism: 1
      schema.change.behavior: try_evolve
  mysql-to-starrocks.yaml: |-
    source:
      type: mysql
      hostname: apnv3-db.default
      port: 3306
      username: apn_admin
      password: L19$a2?FRy-4
      tables: "apnv3.assignment_bill_info,apnv3.assignment_contribution,apnv3.assignment_location,apnv3.assignment_pay_info,apnv3.assignment_pay_rate,apnv3.assignment_timesheet,apnv3.company,apnv3.company_additional_info,apnv3.company_industry_relation,apnv3.company_location,apnv3.company_project_team,apnv3.company_project_team_user,apnv3.company_sales_lead_client_contact,apnv3.company_user_relation,apnv3.company_client_note,apnv3.account_business,apnv3.account_business_service_type_relation,apnv3.business_flow_administrator,apnv3.currency_rate_day,apnv3.enum_currency,apnv3.invoice,apnv3.invoice_activity,apnv3.invoice_client_credit,apnv3.invoice_payment_record,apnv3.job,apnv3.job_additional_info,apnv3.job_note,apnv3.job_company_contact_relation,apnv3.job_job_function_relation,apnv3.job_location,apnv3.job_user_relation,apnv3.talent_recruitment_process_auto_elimination,apnv3.job_talent_recommend_feedback,apnv3.t_contractor_invoice,apnv3.t_email_attachment_record,apnv3.t_group_invoice,apnv3.t_group_invoice_record,apnv3.t_invoice_expense_info,apnv3.t_invoice_timesheet_info,apnv3.t_record_payment_info,apnv3.permission_extra_role_team,apnv3.permission_extra_user_team,apnv3.permission_team,apnv3.permission_user_team,apnv3.recruitment_process,apnv3.recruitment_process_node,apnv3.resume,apnv3.role,apnv3.start,apnv3.start_address,apnv3.start_client_info,apnv3.start_commission,apnv3.start_contract_rate,apnv3.start_failed_warranty,apnv3.start_fte_rate,apnv3.start_fte_salary_package,apnv3.start_rate_change,apnv3.start_residential_address,apnv3.start_termination,apnv3.talent,apnv3.talent_additional_info,apnv3.talent_note,apnv3.talent_tracking_note,apnv3.talent_contact,apnv3.talent_current_location,apnv3.talent_industry_relation,apnv3.talent_job_function_relation,apnv3.talent_language_relation,apnv3.talent_ownership,apnv3.talent_resume_relation,apnv3.talent_user_relation,apnv3.talent_work_authorization_relation,apnv3.talent_recruitment_process,apnv3.talent_recruitment_process_note,apnv3.talent_recruitment_process_commission,apnv3.talent_recruitment_process_eliminate,apnv3.talent_recruitment_process_interview,apnv3.talent_recruitment_process_ipg_agreed_pay_rate,apnv3.talent_recruitment_process_ipg_contract_fee_charge,apnv3.talent_recruitment_process_ipg_fte_salary_package,apnv3.talent_recruitment_process_ipg_offer_accept,apnv3.talent_recruitment_process_ipg_offer_letter_cost_rate,apnv3.talent_recruitment_process_kpi_user,apnv3.talent_recruitment_process_node,apnv3.talent_recruitment_process_offer,apnv3.talent_recruitment_process_offer_fee_charge,apnv3.talent_recruitment_process_offer_salary,apnv3.talent_recruitment_process_offer_salary_package,apnv3.talent_recruitment_process_onboard,apnv3.talent_recruitment_process_onboard_client_info,apnv3.talent_recruitment_process_onboard_date,apnv3.talent_recruitment_process_onboard_work_location,apnv3.talent_recruitment_process_resignation,apnv3.talent_recruitment_process_stop_statistics,apnv3.talent_recruitment_process_submit_to_client,apnv3.talent_recruitment_process_submit_to_job,apnv3.talent_recruitment_process_user_relation,apnv3.tenant,apnv3.tenant_address,apnv3.time_sheet_expense_week_ending_record,apnv3.time_sheet_holiday_record,apnv3.time_sheet_record,apnv3.time_sheet_user,apnv3.time_sheet_week_ending_record,apnv3.timesheet_approve_record,apnv3.timesheet_breaktime_record,apnv3.timesheet_calculate_state,apnv3.timesheet_comments,apnv3.timesheet_expense_record,apnv3.timesheet_google_holiday,apnv3.timesheet_manager,apnv3.timesheet_talent_assignment,apnv3.user,apnv3.user_account,apnv3.user_job_relation,apnv3.user_role,apnv3.enum_business_progress,apnv3.enum_company_client_level,apnv3.enum_company_contact_category,apnv3.enum_company_contact_tag,apnv3.enum_company_sale_lead_source,apnv3.enum_company_service_type,apnv3.enum_company_tag,apnv3.enum_contact_type,apnv3.enum_follow_up_contact_type,apnv3.enum_gender,apnv3.enum_invoicing_service_tax,apnv3.enum_job_priority,apnv3.enum_language,apnv3.enum_motivation,apnv3.enum_receiving_account,apnv3.enum_timezone,apnv3.enum_work_authorization,apnv3.date_dimension,apnv3.credit_transaction"
      server-id: 5490-5500
      server-time-zone: UTC
      scan.newly-added-table.enabled: true
      schema-change.enabled: false
      scan.startup.mode: timestamp
      scan.startup.timestamp-millis: *************
    sink:
      type: starrocks
      name: StarRocks Sink
      username: root
      password: "xw7#]!ZQ\"nDnKa"
      jdbc-url: *******************************************
      load-url: starrocks-fe-service.olap:8030
      table.create.properties.replication_num: 2
      include.schema.changes: [add.column, create.table]
      exclude.schema.changes: [alter.column.type, drop.column, drop.table, rename.column, truncate.table]
    transform:
     -
      filter: assignment_id IS NOT NULL
      source-table: apnv3.assignment_bill_info
     -
      filter: assignment_id IS NOT NULL
      source-table: apnv3.assignment_contribution
     -
      filter: assignment_id IS NOT NULL
      source-table: apnv3.assignment_location
     -
      filter: assignment_id IS NOT NULL
      source-table: apnv3.assignment_pay_info
     -
      filter: assignment_id IS NOT NULL
      source-table: apnv3.assignment_pay_rate
     -
      filter: assignment_id IS NOT NULL
      source-table: apnv3.assignment_timesheet
     -
      filter: company_id IS NOT NULL
      source-table: apnv3.business_flow_administrator
     -
      filter: company_id IS NOT NULL
      source-table: apnv3.company_additional_info
     -
      filter: company_id IS NOT NULL
      source-table: apnv3.company_industry_relation
     -
      filter: company_id IS NOT NULL
      source-table: apnv3.company_location
     -
      filter: company_id IS NOT NULL
      source-table: apnv3.company_project_team
     -
      filter: company_id IS NOT NULL
      source-table: apnv3.company_sales_lead_client_contact
     -
      filter: company_id IS NOT NULL
      source-table: apnv3.company_user_relation
     -
      filter: invoice_id IS NOT NULL
      source-table: apnv3.invoice_activity
     -
      filter: invoice_id IS NOT NULL
      source-table: apnv3.invoice_payment_record
     -
      filter: job_id IS NOT NULL
      source-table: apnv3.job_job_function_relation
     -
      filter: job_id IS NOT NULL
      source-table: apnv3.job_location
     -
      filter: job_id IS NOT NULL
      source-table: apnv3.job_user_relation
     -
      filter: start_id IS NOT NULL
      source-table: apnv3.start_address
     -
      filter: start_id IS NOT NULL
      source-table: apnv3.start_client_info
     -
      filter: start_id IS NOT NULL
      source-table: apnv3.start_commission
     -
      filter: start_id IS NOT NULL
      source-table: apnv3.start_contract_rate
     -
      filter: start_id IS NOT NULL
      source-table: apnv3.start_failed_warranty
     -
      filter: start_id IS NOT NULL
      source-table: apnv3.start_fte_rate
     -
      filter: start_id IS NOT NULL
      source-table: apnv3.start_fte_salary_package
     -
      filter: start_id IS NOT NULL
      source-table: apnv3.start_residential_address
     -
      filter: start_id IS NOT NULL
      source-table: apnv3.start_termination
     -
      filter: invoice_id IS NOT NULL
      source-table: apnv3.t_group_invoice_record
     -
      filter: invoice_id IS NOT NULL
      source-table: apnv3.t_invoice_expense_info
     -
      filter: invoice_id IS NOT NULL
      source-table: apnv3.t_invoice_timesheet_info
     -
      filter: group_invoice_id IS NOT NULL
      source-table: apnv3.t_record_payment_info
     -
      filter: talent_id IS NOT NULL
      source-table: apnv3.talent_contact
     -
      filter: talent_id IS NOT NULL
      source-table: apnv3.talent_current_location
     -
      filter: talent_id IS NOT NULL
      source-table: apnv3.talent_note
     -
      filter: talent_id IS NOT NULL
      source-table: apnv3.talent_ownership
     -
      filter: job_id IS NOT NULL
      source-table: apnv3.talent_recruitment_process_auto_elimination
     -
      filter: talent_recruitment_process_id IS NOT NULL
      source-table: apnv3.talent_recruitment_process_commission
     -
      filter: talent_recruitment_process_id IS NOT NULL
      source-table: apnv3.talent_recruitment_process_eliminate
     -
      filter: talent_recruitment_process_id IS NOT NULL
      source-table: apnv3.talent_recruitment_process_interview
     -
      filter: talent_recruitment_process_id IS NOT NULL
      source-table: apnv3.talent_recruitment_process_ipg_agreed_pay_rate
     -
      filter: talent_recruitment_process_id IS NOT NULL
      source-table: apnv3.talent_recruitment_process_ipg_contract_fee_charge
     -
      filter: talent_recruitment_process_id IS NOT NULL
      source-table: apnv3.talent_recruitment_process_ipg_fte_salary_package
     -
      filter: talent_recruitment_process_id IS NOT NULL
      source-table: apnv3.talent_recruitment_process_ipg_offer_accept
     -
      filter: talent_recruitment_process_id IS NOT NULL
      source-table: apnv3.talent_recruitment_process_kpi_user
     -
      filter: talent_recruitment_process_id IS NOT NULL
      source-table: apnv3.talent_recruitment_process_node
     -
      filter: talent_recruitment_process_id IS NOT NULL
      source-table: apnv3.talent_recruitment_process_note
     -
      filter: talent_recruitment_process_id IS NOT NULL
      source-table: apnv3.talent_recruitment_process_offer
     -
      filter: talent_recruitment_process_id IS NOT NULL
      source-table: apnv3.talent_recruitment_process_offer_fee_charge
     -
      filter: talent_recruitment_process_id IS NOT NULL
      source-table: apnv3.talent_recruitment_process_offer_salary
     -
      filter: talent_recruitment_process_id IS NOT NULL
      source-table: apnv3.talent_recruitment_process_offer_salary_package
     -
      filter: talent_recruitment_process_id IS NOT NULL
      source-table: apnv3.talent_recruitment_process_onboard
     -
      filter: talent_recruitment_process_id IS NOT NULL
      source-table: apnv3.talent_recruitment_process_onboard_client_info
     -
      filter: talent_recruitment_process_id IS NOT NULL
      source-table: apnv3.talent_recruitment_process_onboard_date
     -
      filter: talent_recruitment_process_id IS NOT NULL
      source-table: apnv3.talent_recruitment_process_onboard_work_location
     -
      filter: talent_recruitment_process_id IS NOT NULL
      source-table: apnv3.talent_recruitment_process_resignation
     -
      filter: talent_recruitment_process_id IS NOT NULL
      source-table: apnv3.talent_recruitment_process_stop_statistics
     -
      filter: talent_recruitment_process_id IS NOT NULL
      source-table: apnv3.talent_recruitment_process_submit_to_client
     -
      filter: talent_recruitment_process_id IS NOT NULL
      source-table: apnv3.talent_recruitment_process_submit_to_job
     -
      filter: talent_recruitment_process_id IS NOT NULL
      source-table: apnv3.talent_recruitment_process_user_relation
     -
      filter: talent_id IS NOT NULL
      source-table: apnv3.talent_resume_relation
     -
      filter: talent_id IS NOT NULL
      source-table: apnv3.talent_user_relation
     -
      filter: talent_id IS NOT NULL
      source-table: apnv3.talent_work_authorization_relation
     -
      filter: assignment_id IS NOT NULL
      source-table: apnv3.time_sheet_expense_week_ending_record
     -
      filter: assignment_id IS NOT NULL
      source-table: apnv3.time_sheet_holiday_record
     -
      filter: assignment_id IS NOT NULL
      source-table: apnv3.time_sheet_record
     -
      filter: assignment_id IS NOT NULL
      source-table: apnv3.time_sheet_week_ending_record
     -
      filter: assignment_id IS NOT NULL
      source-table: apnv3.timesheet_breaktime_record
     -
      filter: assignment_id IS NOT NULL
      source-table: apnv3.timesheet_comments
     -
      filter: assignment_id IS NOT NULL
      source-table: apnv3.timesheet_expense_record
     -
      filter: assignment_id IS NOT NULL
      source-table: apnv3.timesheet_manager
     -
      filter: user_id IS NOT NULL
      source-table: apnv3.user_account
     -
      filter: user_id IS NOT NULL
      source-table: apnv3.user_job_relation
     -
      filter: user_id IS NOT NULL
      source-table: apnv3.user_role
    route:
     -
      source-table: apnv3.\.*
      sink-table: ods_apn.<>
      replace-symbol: <>
    pipeline:
      name: Sync APN Database to StarRocks
      parallelism: 1
kind: ConfigMap
metadata:
  name: olap-apn-to-starrocks-configmap
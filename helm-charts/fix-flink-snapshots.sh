#!/bin/bash

# Enhanced FlinkStateSnapshots Fix Script
# This script addresses the root cause of stuck FlinkStateSnapshots and prevents future occurrences

set -e

# Namespace to work with
NAMESPACE="olap"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    print_step "检查先决条件..."
    
    if ! command -v kubectl &> /dev/null; then
        print_error "kubectl 未安装或不在 PATH 中"
        exit 1
    fi
    
    if ! kubectl cluster-info &> /dev/null; then
        print_error "无法连接到 Kubernetes 集群，请检查 kubeconfig"
        exit 1
    fi
    
    if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
        print_error "命名空间 '$NAMESPACE' 不存在"
        exit 1
    fi
    
    print_info "先决条件检查通过"
}

# Analyze the problem
analyze_problem() {
    print_step "分析 FlinkStateSnapshots 问题..."
    
    # Count snapshots by status
    local removing_count=$(kubectl get flinkstatesnapshots -n "$NAMESPACE" -o jsonpath='{.items[?(@.metadata.deletionTimestamp)].metadata.name}' | wc -w)
    local total_count=$(kubectl get flinkstatesnapshots -n "$NAMESPACE" --no-headers | wc -l)
    
    print_info "总共有 $total_count 个 FlinkStateSnapshots"
    print_warning "其中 $removing_count 个处于删除状态（stuck in removing）"
    
    if [ "$removing_count" -gt 0 ]; then
        print_warning "发现卡住的快照，将进行修复..."
        return 0
    else
        print_info "没有发现卡住的快照"
        return 1
    fi
}

# Fix stuck snapshots
fix_stuck_snapshots() {
    print_step "修复卡住的 FlinkStateSnapshots..."
    
    # Get stuck snapshots
    local stuck_snapshots=$(kubectl get flinkstatesnapshots -n "$NAMESPACE" -o jsonpath='{.items[?(@.metadata.deletionTimestamp)].metadata.name}')
    
    if [ -z "$stuck_snapshots" ]; then
        print_info "没有发现卡住的快照"
        return 0
    fi
    
    for snapshot in $stuck_snapshots; do
        print_info "处理快照: $snapshot"
        
        # Remove finalizer using patch
        if kubectl patch flinkstatesnapshot "$snapshot" -n "$NAMESPACE" --type='merge' -p '{"metadata":{"finalizers":null}}' 2>/dev/null; then
            print_info "成功清除 $snapshot 的 finalizer"
            
            # Wait for deletion
            local timeout=30
            while [ $timeout -gt 0 ] && kubectl get flinkstatesnapshot "$snapshot" -n "$NAMESPACE" &>/dev/null; do
                sleep 1
                ((timeout--))
            done
            
            if kubectl get flinkstatesnapshot "$snapshot" -n "$NAMESPACE" &>/dev/null; then
                print_warning "$snapshot 仍然存在，尝试强制删除..."
                kubectl delete flinkstatesnapshot "$snapshot" -n "$NAMESPACE" --force --grace-period=0 2>/dev/null || true
            else
                print_info "$snapshot 已成功删除"
            fi
        else
            print_error "无法清除 $snapshot 的 finalizer"
        fi
    done
}



# Main execution
main() {
    print_info "开始 FlinkStateSnapshots 根本原因修复..."
    print_info "当前 Kubernetes 上下文: $(kubectl config current-context)"
    print_info "工作命名空间: $NAMESPACE"
    echo ""
    
    check_prerequisites
    
    if analyze_problem; then
        fix_stuck_snapshots
    fi

    print_info "修复过程完成！"
}

# Confirm before running
echo -e "${YELLOW}警告: 此脚本将修复卡住的 FlinkStateSnapshots ${NC}"
read -p "$(echo -e ${YELLOW}确定要在命名空间 '$NAMESPACE' 中继续吗？ [y/N]: ${NC})" -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_info "操作已取消"
    exit 0
fi

main

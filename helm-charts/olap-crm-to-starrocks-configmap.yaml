apiVersion: v1
data:
  flink-cdc.yaml: |-
      parallelism: 1
      schema.change.behavior: try_evolve
  mysql-to-starrocks.yaml: |-
    source:

      type: mysql

      hostname: apnv3-db.default

      port: 3306

      username: crm_admin

      password: vPRMX6LsV48i

      tables: crm.\.*

      tables.exclude: crm.databasechangelog,crm.databasechangeloglock,crm.enum_\.*,crm.report_subscription\.*

      server-id: 5500-5510

      server-time-zone: UTC

      scan.newly-added-table.enabled: true

      schema-change.enabled: true

    sink:

      type: starrocks

      name: StarRocks Sink

      username: root

      password: "xw7#]!ZQ\"nDnKa"

      jdbc-url: *******************************************

      load-url: starrocks-fe-service.olap:8030

      table.create.properties.replication_num: 2

      include.schema.changes: [add.column, create.table]
      exclude.schema.changes: [alter.column.type, drop.column, drop.table, rename.column, truncate.table]

    route:
     -

      source-table: crm.\.*

      sink-table: ods_crm.<>

      replace-symbol: <>

    pipeline:

      name: Sync CRM Database to StarRocks

      parallelism: 1
kind: ConfigMap
metadata:
  name: olap-crm-to-starrocks-configmap
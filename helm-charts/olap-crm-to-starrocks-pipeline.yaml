# kubectl delete flinkdeployment mysql-to-starrocks-cdc-pipeline-job -n olap --force --grace-period=0
apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  namespace: olap
  name: olap-crm-to-starrocks-cdc-pipeline-job
spec:
  flinkConfiguration:
    classloader.resolve-order: parent-first
    state.checkpoints.dir: "file:///opt/flink/data/checkpoints"
    state.savepoints.dir: "file:///opt/flink/data/savepoints"
    kubernetes.operator.savepoint.history.max.age: 7d
    kubernetes.operator.savepoint.history.max.count: "3"
    kubernetes.operator.periodic.savepoint.interval: 6h
    kubernetes.operator.periodic.checkpoint.interval: 30s
    kubernetes.operator.savepoint.cleanup.enabled: "true"
    kubernetes.operator.savepoint.dispose-on-delete: "true"
    kubernetes.operator.snapshot.resource.timeout: 10m
    kubernetes.operator.observer.rest-ready.delay: 10s
    kubernetes.operator.reconcile.interval: 30s
    kubernetes.operator.job.restart.failed: "true"
  flinkVersion: v1_20
  image: "minghealtomni/flink_mysql_starrocks:**************"
  imagePullPolicy: IfNotPresent
  restartNonce: 1
  serviceAccount: flink
  job:
    args:
      - '--use-mini-cluster'
      - /opt/flink/flink-cdc-3.4.0/conf/mysql-to-starrocks.yaml
    entryClass: org.apache.flink.cdc.cli.CliFrontend
    jarURI: 'local:///opt/flink/flink-cdc-3.4.0/lib/flink-cdc-dist-3.4.0.jar'
    parallelism: 1
    state: running
    upgradeMode: savepoint
  jobManager:
    replicas: 1
    resource:
      cpu: 0.25
      memory: 1024m
  taskManager:
    resource:
      cpu: 0.5
      memory: 2048m
  podTemplate:
    apiVersion: v1
    kind: Pod
    spec:
      nodeSelector:
        role: "olap-node"
      affinity:
        podAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchLabels:
                component: jobmanager
                app: olap-crm-to-starrocks-cdc-pipeline-job
            topologyKey: kubernetes.io/hostname
      initContainers:
        - name: set-permissions
          image: busybox
          command: ["sh", "-c", "chmod -R 777 /opt/flink/data"]
          volumeMounts:
            - mountPath: /opt/flink/data
              name: flink-state-volume
      imagePullSecrets:
      - name: altomni-docker
      containers:
        # don't modify this name
        - name: flink-main-container
          envFrom:
            - configMapRef:
                name: olap-crm-to-starrocks-configmap
          volumeMounts:
            - mountPath: /opt/flink/flink-cdc-3.4.0/conf
              name: crm-to-starrocks-config
            - mountPath: /opt/flink/data
              name: flink-state-volume
      volumes:
        - name: crm-to-starrocks-config
          configMap:
            name: olap-crm-to-starrocks-configmap
        - name: flink-state-volume
          persistentVolumeClaim:
            claimName: olap-crm-flink-state-pvc

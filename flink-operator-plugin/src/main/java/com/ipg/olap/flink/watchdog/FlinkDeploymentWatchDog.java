package com.ipg.olap.flink.watchdog;

import io.fabric8.kubernetes.api.model.Event;
import org.apache.flink.api.common.JobStatus;
import org.apache.flink.kubernetes.operator.api.FlinkDeployment;
import org.apache.flink.kubernetes.operator.api.FlinkSessionJob;
import org.apache.flink.kubernetes.operator.api.listener.FlinkResourceListener;
import org.apache.flink.kubernetes.operator.api.status.FlinkDeploymentStatus;
import org.apache.flink.kubernetes.operator.api.status.FlinkSessionJobStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

public class FlinkDeploymentWatchDog implements FlinkResourceListener {
    private static final Logger logger = LoggerFactory.getLogger(FlinkDeploymentWatchDog.class);
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
            .withZone(ZoneId.systemDefault());
    
    // 定义值得关注的状态变化
    private static final JobStatus[] CRITICAL_JOB_STATES = {
            JobStatus.FAILED, JobStatus.CANCELED, JobStatus.RECONCILING, JobStatus.INITIALIZING
    };

    @Override
    public void onDeploymentStatusUpdate(StatusUpdateContext<FlinkDeployment, FlinkDeploymentStatus> statusUpdateContext) {
        FlinkDeployment flinkResource = statusUpdateContext.getFlinkResource();
        FlinkDeploymentStatus previousStatus = statusUpdateContext.getPreviousStatus();
        FlinkDeploymentStatus newStatus = statusUpdateContext.getNewStatus();
        Instant timestamp = statusUpdateContext.getTimestamp();

        printlnFlinkDeploymentJobStatus("FlinkDeployment previousStatus", flinkResource, previousStatus);
        printlnFlinkDeploymentJobStatus("FlinkDeployment newStatus", flinkResource, newStatus);
        
        // 检查是否有状态变化
        if (hasSignificantStatusChange(previousStatus, newStatus)) {
            // 构建通知消息
            String message = buildDeploymentStatusChangeMessage(flinkResource, previousStatus, newStatus, timestamp);
            
            try {
                // 发送通知
                logger.info("Sending Lark notification for deployment status change: {}", message);
                LarkNotify.sendAlertToLark(message);
            } catch (Exception e) {
                logger.error("Failed to send Lark notification for deployment status change", e);
            }
        }
    }

    private boolean hasSignificantStatusChange(FlinkDeploymentStatus previousStatus, FlinkDeploymentStatus newStatus) {
        // 如果前一个状态为null，表示是新创建的部署
        if (previousStatus == null || previousStatus.getJobStatus() == null) {
            return true;
        }
        
        // 如果新状态为null，也属于重要变化（通常是错误情况）
        if (newStatus == null || newStatus.getJobStatus() == null) {
            return true;
        }
        
        JobStatus previousJobStatus = previousStatus.getJobStatus().getState();
        JobStatus newJobStatus = newStatus.getJobStatus().getState();
        
        // 如果状态没变化，不需要通知
        if (Objects.equals(previousJobStatus, newJobStatus)) {
            return false;
        }
        
        // 检查是否是关键状态（如FAILED, CANCELED等）
        for (JobStatus criticalState : CRITICAL_JOB_STATES) {
            if (newJobStatus == criticalState) {
                return true;
            }
        }
        
        // 如果从RUNNING变为其他状态，也需要通知
        if (previousJobStatus == JobStatus.RUNNING) {
            return true;
        }
        
        // 如果到达RUNNING状态，需要通知
        return newJobStatus == JobStatus.RUNNING;
    }

    private String buildDeploymentStatusChangeMessage(FlinkDeployment flinkResource, FlinkDeploymentStatus previousStatus, 
                                                      FlinkDeploymentStatus newStatus, Instant timestamp) {
        StringBuilder message = new StringBuilder();
        message.append("🚨 Flink Deployment Status Change Alert 🚨\n\n");
        
        // 添加基本信息
        String namespace = flinkResource.getMetadata().getNamespace();
        String name = flinkResource.getMetadata().getName();
        message.append("📌 **Deployment**: ").append(namespace).append("/").append(name).append("\n");
        message.append("⏰ **Time**: ").append(DATE_FORMATTER.format(timestamp)).append("\n\n");
        
        // 添加状态变化信息
        JobStatus previousJobStatus = previousStatus != null && previousStatus.getJobStatus() != null ? 
                previousStatus.getJobStatus().getState() : null;
        String previousLifecycle = previousStatus != null ? previousStatus.getLifecycleState().name() : "N/A";
        
        JobStatus newJobStatus = newStatus != null && newStatus.getJobStatus() != null ? 
                newStatus.getJobStatus().getState() : null;
        String newLifecycle = newStatus != null ? newStatus.getLifecycleState().name() : "N/A";
        
        message.append("**Status Change**:\n");
        message.append("- Job Status: ").append(previousJobStatus).append(" → ").append(newJobStatus).append("\n");
        message.append("- Lifecycle: ").append(previousLifecycle).append(" → ").append(newLifecycle).append("\n\n");
        
        // 如果有错误信息，添加到通知中
        if (newStatus != null && newStatus.getError() != null && !newStatus.getError().isEmpty()) {
            message.append("**Error**:\n").append(newStatus.getError()).append("\n\n");
        }
        
        message.append("Please check the Kubernetes dashboard or Flink UI for more details.");
        
        return message.toString();
    }

    private void printlnFlinkDeploymentJobStatus(String tag, FlinkDeployment flinkResource, FlinkDeploymentStatus jobStatus) {
        String namespace = flinkResource.getMetadata().getNamespace();
        String jobName = flinkResource.getMetadata().getName(); // NAME
        JobStatus state = jobStatus.getJobStatus().getState(); // JOB STATUS
        String lifecycleState = jobStatus.getLifecycleState().name(); // LIFECYCLE STATE
        logger.info("TAG: {}, NAMESPACE: {}, NAME: {}, JOB STATUS: {}, LIFECYCLE STATE: {}",
            tag,
            namespace,
            jobName,
            state,
            lifecycleState);
    }

    @Override
    public void onDeploymentEvent(ResourceEventContext<FlinkDeployment> resourceEventContext) {
        Event event = resourceEventContext.getEvent();
        FlinkDeployment flinkResource = resourceEventContext.getFlinkResource();
        
        // 构建事件通知
        String message = buildDeploymentEventMessage(flinkResource, event);
        
        try {
            // 发送通知
            logger.info("Sending Lark notification for deployment event: {}", message);
            LarkNotify.sendAlertToLark(message);
        } catch (Exception e) {
            logger.error("Failed to send Lark notification for deployment event", e);
        }
    }
    
    private String buildDeploymentEventMessage(FlinkDeployment flinkResource, Event event) {
        StringBuilder message = new StringBuilder();
        message.append("🔔 Flink Deployment Event Alert 🔔\n\n");
        
        // 添加基本信息
        String namespace = flinkResource.getMetadata().getNamespace();
        String name = flinkResource.getMetadata().getName();
        message.append("📌 **Deployment**: ").append(namespace).append("/").append(name).append("\n");
        
        // 添加事件信息
        message.append("🔄 **Action**: ").append(event.getAction()).append("\n");
        message.append("⏰ **Time**: ").append(DATE_FORMATTER.format(
                Instant.parse(event.getLastTimestamp()))).append("\n");
        message.append("👤 **Source**: ").append(event.getSource().getComponent()).append("\n");
        
        // 添加事件原因和消息
        message.append("📝 **Reason**: ").append(event.getReason()).append("\n");
        message.append("📄 **Message**: ").append(event.getMessage()).append("\n\n");
        
        message.append("Please check the Kubernetes dashboard or Flink UI for more details.");
        
        return message.toString();
    }

    @Override
    public void onSessionJobStatusUpdate(StatusUpdateContext<FlinkSessionJob, FlinkSessionJobStatus> statusUpdateContext) {
        FlinkSessionJob flinkResource = statusUpdateContext.getFlinkResource();
        FlinkSessionJobStatus previousStatus = statusUpdateContext.getPreviousStatus();
        FlinkSessionJobStatus newStatus = statusUpdateContext.getNewStatus();
        Instant timestamp = statusUpdateContext.getTimestamp();

        printlnFlinkSessionJobStatus("FlinkSessionJob previousStatus", flinkResource, previousStatus);
        printlnFlinkSessionJobStatus("FlinkSessionJob newStatus", flinkResource, newStatus);
    }

    private void printlnFlinkSessionJobStatus(String tag, FlinkSessionJob flinkResource, FlinkSessionJobStatus jobStatus) {
        String namespace = flinkResource.getMetadata().getNamespace();
        String jobName = flinkResource.getMetadata().getName(); // NAME
        JobStatus state = jobStatus.getJobStatus().getState(); // JOB STATUS
        String lifecycleState = jobStatus.getLifecycleState().name(); // LIFECYCLE STATE
        logger.info("TAG: {}, NAMESPACE: {}, NAME: {}, JOB STATUS: {}, LIFECYCLE STATE: {}",
            tag,
            namespace,
            jobName,
            state,
            lifecycleState);
    }

    @Override
    public void onSessionJobEvent(ResourceEventContext<FlinkSessionJob> resourceEventContext) {
        // 可选：如果也需要监控SessionJob事件，可以类似于onDeploymentEvent实现
    }

    @Override
    public void onStateSnapshotEvent(FlinkStateSnapshotEventContext ctx) {
        // 可选：如果需要监控状态快照事件，可以在这里实现
    }

    @Override
    public void onStateSnapshotStatusUpdate(FlinkStateSnapshotStatusUpdateContext ctx) {
        // 可选：如果需要监控状态快照状态更新，可以在这里实现
    }
}

package com.ipg.olap.flink.watchdog;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

public class LarkNotify {

    private static final Logger log = LoggerFactory.getLogger(LarkNotify.class);

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private static final int MAX_RETRIES = 3;
    private static final int RETRY_DELAY_MS = 1000;
    private static final int TIMEOUT_MILLIS = 10000;
    private static final String HMAC_SHA256 = "HmacSHA256";

    public static void sendAlertToLark(String msg) {
        String webhookUrl = System.getenv("LARK_WEBHOOK_URL");
        String webhookKey = System.getenv("LARK_WEBHOOK_KEY");
        if (webhookUrl == null || webhookUrl.isEmpty() || webhookKey == null || webhookKey.isEmpty()) {
            log.error("[Flink Operator Watchdog: sendLarkNotification] Lark webhook configuration is missing");
            throw new RuntimeException("Lark webhook url or key is not set. Please set the environment variable LARK_WEBHOOK_URL and LARK_WEBHOOK_KEY.");
        }

        int retryCount = 0;
        Exception lastException = null;

        while (retryCount < MAX_RETRIES) {
            try {
                sendRequest(webhookUrl, webhookKey, msg);
                return;
            } catch (Exception e) {
                lastException = e;
                retryCount++;
                if (retryCount < MAX_RETRIES) {
                    log.warn("[Flink Operator Watchdog: sendLarkNotification] Attempt {} failed, retrying in {} ms. Error: {}",
                            retryCount, RETRY_DELAY_MS, e.getMessage());
                    try {
                        Thread.sleep(RETRY_DELAY_MS);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
        }

        log.error("[Flink Operator Watchdog: sendLarkNotification] Failed to send notification after {} attempts", MAX_RETRIES, lastException);
        throw new RuntimeException("Failed to send Lark notification after " + MAX_RETRIES + " attempts", lastException);
    }

    private static void sendRequest(String webhookUrl, String webhookKey, String msg) throws IOException {
        Long timestamp = Instant.now().getEpochSecond();
        String stringToSign = timestamp + "\n" + webhookKey;
        
        // 使用JDK原生实现计算HMAC-SHA256签名
        String signature;
        try {
            Mac mac = Mac.getInstance(HMAC_SHA256);
            SecretKeySpec secretKeySpec = new SecretKeySpec(stringToSign.getBytes(StandardCharsets.UTF_8), HMAC_SHA256);
            mac.init(secretKeySpec);
            byte[] bytes = mac.doFinal();
            signature = Base64.getEncoder().encodeToString(bytes);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            throw new IOException("Failed to calculate HMAC-SHA256", e);
        }

        Map<String, Object> json = new HashMap<>();
        json.put("timestamp", timestamp);
        json.put("sign", signature);
        json.put("msg_type", "text");
        json.put("content", new HashMap<>() {{
            put("text", msg);
        }});
        
        String bodyJson = OBJECT_MAPPER.writeValueAsString(json);
        
        // Create connection
        URL url = new URL(webhookUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        
        // Set up the connection
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json; charset=utf-8");
        connection.setRequestProperty("Accept", "application/json");
        connection.setDoOutput(true);
        connection.setConnectTimeout(TIMEOUT_MILLIS);
        connection.setReadTimeout(TIMEOUT_MILLIS);
        
        // Send the request
        try (OutputStream os = connection.getOutputStream()) {
            byte[] input = bodyJson.getBytes(StandardCharsets.UTF_8);
            os.write(input, 0, input.length);
        }
        
        // Get the response
        int responseCode = connection.getResponseCode();
        StringBuilder responseBody = new StringBuilder();
        
        try (BufferedReader br = new BufferedReader(
                new InputStreamReader(responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream(), 
                        StandardCharsets.UTF_8))) {
            String line;
            while ((line = br.readLine()) != null) {
                responseBody.append(line);
            }
        }
        
        log.info("[Flink Operator Watchdog: sendLarkNotification] Lark response: {}", responseBody);
        
        if (responseCode >= 400) {
            throw new IOException("Failed to send notification. Response code: " + responseCode + ", Body: " + responseBody);
        }
        
        // Close the connection
        connection.disconnect();
    }
}
